`include "config.svh"

module controller (
    input logic clock,
    input logic reset_n,
    input logic [15:0] keyboard,
    input logic [4:0] button,
    output logic [7:0][5:0] display_code,
    output logic [31:0] frequency_select
);

    // 直接使用clock，无需中间信号

    // Timing generating part

    logic T[0:3];
    timing #(
        .timing_count(4)
    ) timer (
        .clock(clock),
        .reset_n(reset_n),
        .clock_out(T)
    );

    // Part 0 - 合并的输入控制器

    logic [15:0] keyboard_down;
    logic [4:0] button_down;
    logic [2:0] h_ptr;
    logic [1:0] v_ptr;

    input_controller ic_ins (
        .clock(T[0]),
        .reset_n(reset_n),
        .keyboard(keyboard),
        .button(button),
        .keyboard_down(keyboard_down),
        .button_down(button_down),
        .h_add(button_down[1]),
        .h_sub(button_down[0]),
        .v_add(button_down[4]),  // S5按键用于垂直上移
        .v_sub(button_down[2]),  // S3按键用于垂直下移
        .state(state),           // 添加状态信号
        .h_ptr(h_ptr),
        .v_ptr(v_ptr)
    );

    // Part 1 - 状态机 (内联实现)

    state_t state;
    always @(posedge T[1] or negedge reset_n) begin
        if (~reset_n) begin
            state <= normal_state;
        end else begin
            // 优先处理倒计时模式切换
            if (button_down[3] && state == timer_setting_state) begin
                // 在正计时模式下按S4进入倒计时模式
                state <= countdown_setting_state;
            end else if (button_down[3] && state == countdown_setting_state) begin
                // 在倒计时模式下按S4返回正常模式
                state <= normal_state;
            end else if (button_down[3]) begin
                // S4按键循环切换四种模式
                case (state)
                    normal_state: state <= time_setting_state;
                    time_setting_state: state <= alarm_setting_state;
                    alarm_setting_state: state <= timer_setting_state;
                    timer_setting_state: state <= normal_state;
                    countdown_setting_state: state <= normal_state;
                    default: state <= normal_state;
                endcase
            end
            // 注意：不需要else分支，状态会自动保持
        end
    end

    // Part 2

    logic [23:0] time_data;
    logic [`ALARM_COUNT-1:0][23:0] alarm_data;
    logic [`ALARM_COUNT-1:0] is_activated;
    logic [23:0] timer_data;  // 添加计时器数据
    logic timer_running;  // 添加计时器运行状态
    logic [23:0] countdown_data;  // 添加倒计时数据
    logic countdown_running;  // 添加倒计时运行状态
    logic countdown_finished;  // 添加倒计时完成信号


    logic keyboard_warning;
    logic zero_ms;
    data_modifier dm_ins (
        .clock(T[2]),
        .reset_n(reset_n),
        .state(state),
        .h_ptr(h_ptr),
        .v_ptr(v_ptr),
        .keyboard_down(keyboard_down),
        .button_down(button_down),  // 添加按键输入
        .time_data(time_data),
        .alarm_data(alarm_data),
        .is_activated(is_activated),
        .timer_data(timer_data),  // 添加计时器数据输出
        .timer_running(timer_running),  // 添加计时器运行状态输出
        .countdown_data(countdown_data),  // 添加倒计时数据输出
        .countdown_running(countdown_running),  // 添加倒计时运行状态输出
        .countdown_finished(countdown_finished),  // 添加倒计时完成信号输出
        .keyboard_warning(keyboard_warning),
        .zero_ms(zero_ms)
    );



    logic pointer_warning;
    assign pointer_warning = (button_down[0] & button_down[1]) | (button_down[2] & button_down[4]);

    logic [`ALARM_COUNT-1:0] alarm_signal;
    logic alarm;
    logic oclock;
    logic warning;
    logic no_response;

    // 内联闹钟比较逻辑
    generate
        for (genvar i = 0; i < `ALARM_COUNT; i++) begin : alarm_compare
            assign alarm_signal[i] = is_activated[i] & (time_data == alarm_data[i]);
        end
    endgenerate

    assign alarm = (|alarm_signal) & zero_ms;  // ?
    assign oclock = (~(|{time_data[15:0]})) & zero_ms;
    assign warning = keyboard_warning | pointer_warning;
    assign no_response = |button_down;

    // Part 3

    display_data_generator ddg_ins (
        .clock(T[3]),
        .reset_n(reset_n),
        .state(state),
        .h_ptr(h_ptr),
        .v_ptr(v_ptr),
        .time_data(time_data),
        .alarm_data(alarm_data),
        .is_activated(is_activated),
        .timer_data(timer_data),  // 添加计时器数据
        .timer_running(timer_running),  // 添加计时器运行状态
        .countdown_data(countdown_data),  // 添加倒计时数据
        .countdown_running(countdown_running),  // 添加倒计时运行状态
        .display_code(display_code)
    );

    audio_output_generator aog_ins (
        .clock(T[3]),
        .reset_n(reset_n),
        .no_response(no_response),
        .alarm(alarm),
        .oclock(oclock),
        .warning(warning),
        .countdown_finished(countdown_finished),  // 添加倒计时完成信号
        .frequency_select(frequency_select)
    );

endmodule
