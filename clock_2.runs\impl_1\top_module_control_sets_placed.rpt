Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
---------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Tue Jun  3 22:39:13 2025
| Host         : DESKTOP-5LO6MT6 running 64-bit major release  (build 9200)
| Command      : report_control_sets -verbose -file top_module_control_sets_placed.rpt
| Design       : top_module
| Device       : xc7a100t
---------------------------------------------------------------------------------------

Control Set Information

Table of Contents
-----------------
1. Summary
2. Histogram
3. Flip-Flop Distribution
4. Detailed Control Set Information

1. Summary
----------

+----------------------------------------------------------+-------+
|                          Status                          | Count |
+----------------------------------------------------------+-------+
| Number of unique control sets                            |    74 |
| Unused register locations in slices containing registers |   235 |
+----------------------------------------------------------+-------+


2. Histogram
------------

+--------+--------------+
| Fanout | Control Sets |
+--------+--------------+
|      3 |            2 |
|      4 |           45 |
|     15 |           16 |
|    16+ |           11 |
+--------+--------------+


3. Flip-Flop Distribution
-------------------------

+--------------+-----------------------+------------------------+-----------------+--------------+
| Clock Enable | Synchronous Set/Reset | Asynchronous Set/Reset | Total Registers | Total Slices |
+--------------+-----------------------+------------------------+-----------------+--------------+
| No           | No                    | No                     |               0 |            0 |
| No           | No                    | Yes                    |             426 |          171 |
| No           | Yes                   | No                     |               0 |            0 |
| Yes          | No                    | No                     |               0 |            0 |
| Yes          | No                    | Yes                    |             555 |          169 |
| Yes          | Yes                   | No                     |               0 |            0 |
+--------------+-----------------------+------------------------+-----------------+--------------+


4. Detailed Control Set Information
-----------------------------------

+--------------------------+----------------------------------------------------------------+------------------+------------------+----------------+
|       Clock Signal       |                          Enable Signal                         | Set/Reset Signal | Slice Load Count | Bel Load Count |
+--------------------------+----------------------------------------------------------------+------------------+------------------+----------------+
|  c_ins/timer/T[1]_0      |                                                                | reset_IBUF       |                1 |              3 |
|  dtb_ins/dtb_divider/CLK |                                                                | reset_IBUF       |                1 |              3 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_17[0]                                | reset_IBUF       |                2 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[2]_6[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_14[0]                                | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[2]_5[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_13[0]                                | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[2]_11[0]                                | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[2]_7[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/dm_ins/timer_data[5][3]_i_1_n_0                          | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_11[0]                                | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_5[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_12[0]                                | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[2]_13[0]                                | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[2]_8[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[2]_9[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[2]_4[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/E[0]                                              | reset_IBUF       |                3 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[0]_2[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[0]_5[0]                                 | reset_IBUF       |                3 |              4 |
|  T[2]_1                  | c_ins/ic_ins/button_down_reg[4]_0[0]                           | reset_IBUF       |                2 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_10[0]                                | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/button_down_reg[4]_2[0]                           | reset_IBUF       |                2 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[0]_0[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[0]_1[0]                                 | reset_IBUF       |                1 |              4 |
|  k_ins/k_div_ins/CLK     |                                                                | reset_IBUF       |                1 |              4 |
|  k_ins/k_div_ins/CLK     | k_ins/inner_keyboard[1]                                        | reset_IBUF       |                1 |              4 |
|  k_ins/k_div_ins/CLK     | k_ins/inner_keyboard[0]                                        | reset_IBUF       |                1 |              4 |
|  k_ins/k_div_ins/CLK     | k_ins/inner_keyboard[2]                                        | reset_IBUF       |                2 |              4 |
|  k_ins/k_div_ins/CLK     | k_ins/inner_keyboard[3]                                        | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/button_down_reg[4]_1[0]                           | reset_IBUF       |                3 |              4 |
|  T[2]_1                  | c_ins/dm_ins/timer_data[1][3]_i_1_n_0                          | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/dm_ins/timer_data[2][3]_i_1_n_0                          | reset_IBUF       |                2 |              4 |
|  T[2]_1                  | c_ins/dm_ins/timer_data[3][3]_i_1_n_0                          | reset_IBUF       |                2 |              4 |
|  T[2]_1                  | c_ins/dm_ins/timer_data[0][3]_i_1_n_0                          | reset_IBUF       |                2 |              4 |
|  T[2]_1                  | c_ins/dm_ins/time_data[5][3]_i_1_n_0                           | reset_IBUF       |                2 |              4 |
|  T[2]_1                  | c_ins/dm_ins/timer_data[4][3]_i_1_n_0                          | reset_IBUF       |                2 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[0]_3[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_16[0]                                | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[2]_0[0]                                 | reset_IBUF       |                2 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[2]_10[0]                                | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_8[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_9[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_6[0]                                 | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[2]_12[0]                                | reset_IBUF       |                2 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_15[0]                                | reset_IBUF       |                1 |              4 |
|  T[2]_1                  | c_ins/ic_ins/h_ptr_reg[1]_7[0]                                 | reset_IBUF       |                1 |              4 |
|  clock_IBUF_BUFG         | k_ins/genblk1[0].debouncer_gen[0].deb_ins/count[0]_i_1__4_n_0  | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[0].debouncer_gen[2].deb_ins/count[0]_i_1__6_n_0  | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[2].debouncer_gen[3].deb_ins/count[0]_i_1__15_n_0 | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[3].debouncer_gen[0].deb_ins/count[0]_i_1__16_n_0 | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[3].debouncer_gen[1].deb_ins/count[0]_i_1__17_n_0 | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[3].debouncer_gen[2].deb_ins/count[0]_i_1__18_n_0 | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[3].debouncer_gen[3].deb_ins/count[0]_i_1__19_n_0 | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[0].debouncer_gen[3].deb_ins/count[0]_i_1__7_n_0  | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[1].debouncer_gen[0].deb_ins/count[0]_i_1__8_n_0  | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[1].debouncer_gen[1].deb_ins/count[0]_i_1__9_n_0  | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[1].debouncer_gen[2].deb_ins/count[0]_i_1__10_n_0 | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[1].debouncer_gen[3].deb_ins/count[0]_i_1__11_n_0 | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[2].debouncer_gen[0].deb_ins/count[0]_i_1__12_n_0 | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[2].debouncer_gen[1].deb_ins/count[0]_i_1__13_n_0 | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[2].debouncer_gen[2].deb_ins/count[0]_i_1__14_n_0 | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | k_ins/genblk1[0].debouncer_gen[1].deb_ins/count[0]_i_1__5_n_0  | reset_IBUF       |                4 |             15 |
|  clock_IBUF_BUFG         | b_ins/debouncer_gen[3].deb_ins/count[0]_i_1__2_n_0             | reset_IBUF       |                5 |             20 |
|  clock_IBUF_BUFG         | b_ins/debouncer_gen[4].deb_ins/count[0]_i_1__3_n_0             | reset_IBUF       |                5 |             20 |
|  clock_IBUF_BUFG         | b_ins/debouncer_gen[1].deb_ins/count[0]_i_1__0_n_0             | reset_IBUF       |                5 |             20 |
|  clock_IBUF_BUFG         | b_ins/debouncer_gen[2].deb_ins/count[0]_i_1__1_n_0             | reset_IBUF       |                5 |             20 |
|  clock_IBUF_BUFG         | b_ins/debouncer_gen[0].deb_ins/count[0]_i_1_n_0                | reset_IBUF       |                5 |             20 |
| ~clock_IBUF_BUFG         |                                                                | reset_IBUF       |                9 |             33 |
|  T[3]_0                  | c_ins/ddg_ins/reset_n                                          | reset_IBUF       |               19 |             39 |
|  T[2]_1                  |                                                                | reset_IBUF       |               15 |             40 |
|  T[0]_2                  |                                                                | reset_IBUF       |               13 |             47 |
|  clock_IBUF_BUFG         |                                                                | reset_IBUF       |               55 |            112 |
|  T[3]_0                  |                                                                | reset_IBUF       |               76 |            184 |
+--------------------------+----------------------------------------------------------------+------------------+------------------+----------------+


