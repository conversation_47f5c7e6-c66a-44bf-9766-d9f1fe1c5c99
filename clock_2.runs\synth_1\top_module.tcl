# 
# Synthesis run script generated by <PERSON><PERSON>
# 

set TIME_start [clock seconds] 
proc create_report { reportName command } {
  set status "."
  append status $reportName ".fail"
  if { [file exists $status] } {
    eval file delete [glob $status]
  }
  send_msg_id runtcl-4 info "Executing : $command"
  set retval [eval catch { $command } msg]
  if { $retval != 0 } {
    set fp [open $status w]
    close $fp
    send_msg_id runtcl-5 warning "$msg"
  }
}
set_param xicom.use_bs_reader 1
create_project -in_memory -part xc7a100tfgg484-1

set_param project.singleFileAddWarning.threshold 0
set_param project.compositeFile.enableAutoGeneration 0
set_param synth.vivado.isSynthRun true
set_property webtalk.parent_dir E:/YS/clock_2/clock_2.cache/wt [current_project]
set_property parent.project_path E:/YS/clock_2/clock_2.xpr [current_project]
set_property default_lib xil_defaultlib [current_project]
set_property target_language Verilog [current_project]
set_property ip_output_repo e:/YS/clock_2/clock_2.cache/ip [current_project]
set_property ip_cache_permissions {read write} [current_project]
read_verilog {
  E:/YS/clock_2/src/hdl/config.svh
  E:/YS/clock_2/src/hdl/config/system_config.svh
  E:/YS/clock_2/src/hdl/config/display_config.svh
  E:/YS/clock_2/src/hdl/config/input_config.svh
}
set_property file_type "Verilog Header" [get_files E:/YS/clock_2/src/hdl/config.svh]
set_property file_type "Verilog Header" [get_files E:/YS/clock_2/src/hdl/config/system_config.svh]
set_property file_type "Verilog Header" [get_files E:/YS/clock_2/src/hdl/config/display_config.svh]
set_property file_type "Verilog Header" [get_files E:/YS/clock_2/src/hdl/config/input_config.svh]
read_verilog -library xil_defaultlib -sv {
  E:/YS/clock_2/src/hdl/modules/audio_generator.sv
  E:/YS/clock_2/src/hdl/controller/audio_output_generator.sv
  E:/YS/clock_2/src/hdl/modules/buttons.sv
  E:/YS/clock_2/src/hdl/controller/controller.sv
  E:/YS/clock_2/src/hdl/controller/data_modifier.sv
  E:/YS/clock_2/src/hdl/utils/debouncer.sv
  E:/YS/clock_2/src/hdl/modules/digital_tube.sv
  E:/YS/clock_2/src/hdl/controller/display_data_generator.sv
  E:/YS/clock_2/src/hdl/utils/divider.sv
  E:/YS/clock_2/src/hdl/controller/input_controller.sv
  E:/YS/clock_2/src/hdl/modules/keyboards.sv
  E:/YS/clock_2/src/hdl/utils/segment_decoder.sv
  E:/YS/clock_2/src/hdl/utils/synchronizer.sv
  E:/YS/clock_2/src/hdl/utils/timing.sv
  E:/YS/clock_2/src/hdl/utils/v_divider.sv
  E:/YS/clock_2/src/hdl/top_module.sv
}
# Mark all dcp files as not used in implementation to prevent them from being
# stitched into the results of this synthesis run. Any black boxes in the
# design are intentionally left as such for best results. Dcp files will be
# stitched into the design at a later time, either when this synthesis run is
# opened, or when it is stitched into a dependent implementation run.
foreach dcp [get_files -quiet -all -filter file_type=="Design\ Checkpoint"] {
  set_property used_in_implementation false $dcp
}
read_xdc E:/YS/clock_2/src/xdc/constraints.xdc
set_property used_in_implementation false [get_files E:/YS/clock_2/src/xdc/constraints.xdc]

set_param ips.enableIPCacheLiteLoad 1
close [open __synthesis_is_running__ w]

synth_design -top top_module -part xc7a100tfgg484-1


# disable binary constraint mode for synth run checkpoints
set_param constraints.enableBinaryConstraints false
write_checkpoint -force -noxdef top_module.dcp
create_report "synth_1_synth_report_utilization_0" "report_utilization -file top_module_utilization_synth.rpt -pb top_module_utilization_synth.pb"
file delete __synthesis_is_running__
close [open __synthesis_is_complete__ w]
