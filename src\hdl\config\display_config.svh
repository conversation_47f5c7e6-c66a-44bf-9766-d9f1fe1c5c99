`ifndef _DISPLAY_CONFIG_H_
`define _DISPLAY_CONFIG_H_

// ========================================
// 显示字符编码定义
// ========================================
`define CHAR_BLANK 6'h3f     // 空白字符
`define CHAR_0 6'h00         // 数字'0'
`define CHAR_1 6'h01         // 数字'1'
`define CHAR_2 6'h02         // 数字'2'
`define CHAR_3 6'h03         // 数字'3'
`define CHAR_4 6'h04         // 数字'4'
`define CHAR_5 6'h05         // 数字'5'
`define CHAR_6 6'h06         // 数字'6'
`define CHAR_7 6'h07         // 数字'7'
`define CHAR_8 6'h08         // 数字'8'
`define CHAR_9 6'h09         // 数字'9'
`define CHAR_A 6'h0a         // 字符'A' - 闹钟激活指示
`define CHAR_C 6'h0c         // 字符'C' - 倒计时模式指示
`define CHAR_E 6'h0e         // 字符'E' - 运行状态指示
`define CHAR_F 6'h0f         // 字符'F' - 运行状态指示
`define CHAR_T 6'h0b         // 字符't' - 计时器模式指示

// ========================================
// 数码管配置
// ========================================
`define TUBE_COUNT 8         // 数码管数量
`define SEGMENT_COUNT 7      // 段数
`define DISPLAY_REFRESH_FREQ 100  // 显示刷新频率

`endif
