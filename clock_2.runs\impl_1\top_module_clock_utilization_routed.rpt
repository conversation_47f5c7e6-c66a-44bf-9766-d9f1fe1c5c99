Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
----------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Tue Jun  3 22:39:35 2025
| Host         : DESKTOP-5LO6MT6 running 64-bit major release  (build 9200)
| Command      : report_clock_utilization -file top_module_clock_utilization_routed.rpt
| Design       : top_module
| Device       : 7a100t-fgg484
| Speed File   : -1  PRODUCTION 1.23 2018-06-13
----------------------------------------------------------------------------------------

Clock Utilization Report

Table of Contents
-----------------
1. Clock Primitive Utilization
2. Global Clock Resources
3. Global Clock Source Details
4. Local Clock Details
5. Clock Regions: Key Resource Utilization
6. Clock Regions : Global Clock Summary
7. Device Cell Placement Summary for Global Clock g0
8. Device Cell Placement Summary for Global Clock g1
9. Device Cell Placement Summary for Global Clock g2
10. Device Cell Placement Summary for Global Clock g3
11. Clock Region Cell Placement per Global Clock: Region X1Y1
12. Clock Region Cell Placement per Global Clock: Region X0Y2
13. Clock Region Cell Placement per Global Clock: Region X1Y2
14. Clock Region Cell Placement per Global Clock: Region X0Y3

1. Clock Primitive Utilization
------------------------------

+----------+------+-----------+-----+--------------+--------+
| Type     | Used | Available | LOC | Clock Region | Pblock |
+----------+------+-----------+-----+--------------+--------+
| BUFGCTRL |    4 |        32 |   0 |            0 |      0 |
| BUFH     |    0 |        96 |   0 |            0 |      0 |
| BUFIO    |    0 |        24 |   0 |            0 |      0 |
| BUFMR    |    0 |        12 |   0 |            0 |      0 |
| BUFR     |    0 |        24 |   0 |            0 |      0 |
| MMCM     |    0 |         6 |   0 |            0 |      0 |
| PLL      |    0 |         6 |   0 |            0 |      0 |
+----------+------+-----------+-----+--------------+--------+


2. Global Clock Resources
-------------------------

+-----------+-----------+-----------------+------------+---------------+--------------+-------------------+-------------+-----------------+--------------+---------+------------------------------+-----------------+
| Global Id | Source Id | Driver Type/Pin | Constraint | Site          | Clock Region | Load Clock Region | Clock Loads | Non-Clock Loads | Clock Period | Clock   | Driver Pin                   | Net             |
+-----------+-----------+-----------------+------------+---------------+--------------+-------------------+-------------+-----------------+--------------+---------+------------------------------+-----------------+
| g0        | src0      | BUFG/O          | None       | BUFGCTRL_X0Y0 | n/a          |                 4 |         485 |               0 |       10.000 | sys_clk | clock_IBUF_BUFG_inst/O       | clock_IBUF_BUFG |
| g1        | src1      | BUFG/O          | None       | BUFGCTRL_X0Y1 | n/a          |                 1 |         223 |               0 |              |         | display_code_reg[7][4]_i_3/O | T[3]_0          |
| g2        | src2      | BUFG/O          | None       | BUFGCTRL_X0Y2 | n/a          |                 1 |         200 |               0 |              |         | timer_data_reg[0][3]_i_3/O   | T[2]_1          |
| g3        | src3      | BUFG/O          | None       | BUFGCTRL_X0Y3 | n/a          |                 2 |          47 |               0 |              |         | last_keyboard_reg[15]_i_1/O  | T[0]_2          |
+-----------+-----------+-----------------+------------+---------------+--------------+-------------------+-------------+-----------------+--------------+---------+------------------------------+-----------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)


3. Global Clock Source Details
------------------------------

+-----------+-----------+-----------------+------------+--------------+--------------+-------------+-----------------+---------------------+--------------+--------------------------------------+----------------------------+
| Source Id | Global Id | Driver Type/Pin | Constraint | Site         | Clock Region | Clock Loads | Non-Clock Loads | Source Clock Period | Source Clock | Driver Pin                           | Net                        |
+-----------+-----------+-----------------+------------+--------------+--------------+-------------+-----------------+---------------------+--------------+--------------------------------------+----------------------------+
| src0      | g0        | IBUF/O          | IOB_X0Y74  | IOB_X0Y74    | X0Y1         |           1 |               1 |              10.000 | sys_clk      | clock_IBUF_inst/O                    | clock_IBUF                 |
| src1      | g1        | LUT2/O          | None       | SLICE_X52Y96 | X1Y1         |           1 |               0 |                     |              | c_ins/timer/display_code[7][4]_i_6/O | c_ins/timer/count_reg[1]_0 |
| src2      | g2        | LUT2/O          | None       | SLICE_X52Y96 | X1Y1         |           1 |               0 |                     |              | c_ins/timer/timer_data[0][3]_i_7/O   | c_ins/timer/count_reg[1]_1 |
| src3      | g3        | LUT2/O          | None       | SLICE_X52Y96 | X1Y1         |           1 |               0 |                     |              | c_ins/timer/last_keyboard[15]_i_2/O  | c_ins/timer/count_reg[1]_2 |
+-----------+-----------+-----------------+------------+--------------+--------------+-------------+-----------------+---------------------+--------------+--------------------------------------+----------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)


4. Local Clock Details
----------------------

+----------+-----------------+------------+-------------------+--------------+-------------+-----------------+--------------+-------+-------------------------------------+-------------------------+
| Local Id | Driver Type/Pin | Constraint | Site/BEL          | Clock Region | Clock Loads | Non-Clock Loads | Clock Period | Clock | Driver Pin                          | Net                     |
+----------+-----------------+------------+-------------------+--------------+-------------+-----------------+--------------+-------+-------------------------------------+-------------------------||
| 0        | FDCE/Q          | None       | SLICE_X36Y122/BFF | X0Y2         |          20 |               1 |              |       | k_ins/k_div_ins/clock_pos_reg/Q     | k_ins/k_div_ins/CLK     - Static -
| 1        | FDCE/Q          | None       | SLICE_X52Y138/AFF | X1Y2         |           3 |               1 |              |       | dtb_ins/dtb_divider/clock_pos_reg/Q | dtb_ins/dtb_divider/CLK - Static -
+----------+-----------------+------------+-------------------+--------------+-------------+-----------------+--------------+-------+-------------------------------------+-------------------------||
* Local Clocks in this context represents only clocks driven by non-global buffers
** Clock Loads column represents the clock pin loads (pin count)
*** Non-Clock Loads column represents the non-clock pin loads (pin count)


5. Clock Regions: Key Resource Utilization
------------------------------------------

+-------------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+
|                   | Global Clock |     BUFRs    |    BUFMRs    |    BUFIOs    |     MMCM     |      PLL     |      GT      |      PCI     |    ILOGIC    |    OLOGIC    |      FF      |     LUTM     |    RAMB18    |    RAMB36    |    DSP48E2   |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
| Clock Region Name | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
| X0Y0              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  2600 |    0 |   600 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y0              |    0 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     4 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |  1500 |    0 |   550 |    0 |    40 |    0 |    20 |    0 |    40 |
| X0Y1              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  2000 |    0 |   600 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y1              |    1 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    2 |  1900 |    0 |   650 |    0 |    60 |    0 |    30 |    0 |    40 |
| X0Y2              |    4 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |  830 |  2000 |  232 |   600 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y2              |    2 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |  144 |  1900 |   31 |   650 |    0 |    60 |    0 |    30 |    0 |    40 |
| X0Y3              |    1 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    2 |  2600 |    0 |   600 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y3              |    0 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     4 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |  1350 |    0 |   500 |    0 |    30 |    0 |    15 |    0 |    40 |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
* Global Clock column represents track count; while other columns represents cell counts


6. Clock Regions : Global Clock Summary
---------------------------------------

All Modules
+----+----+----+
|    | X0 | X1 |
+----+----+----+
| Y3 |  1 |  0 |
| Y2 |  4 |  2 |
| Y1 |  0 |  1 |
| Y0 |  0 |  0 |
+----+----+----+


7. Device Cell Placement Summary for Global Clock g0
----------------------------------------------------

+-----------+-----------------+-------------------+---------+-------------+---------------+-------------+----------+----------------+----------+-----------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock   | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net             |
+-----------+-----------------+-------------------+---------+-------------+---------------+-------------+----------+----------------+----------+-----------------+
| g0        | BUFG/O          | n/a               | sys_clk |      10.000 | {0.000 5.000} |         485 |        0 |              0 |        0 | clock_IBUF_BUFG |
+-----------+-----------------+-------------------+---------+-------------+---------------+-------------+----------+----------------+----------+-----------------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+------+------+
|    | X0   | X1   |
+----+------+------+
| Y3 |    2 |    0 |
| Y2 |  343 |  138 |
| Y1 |    0 |    2 |
| Y0 |    0 |    0 |
+----+------+------+


8. Device Cell Placement Summary for Global Clock g1
----------------------------------------------------

+-----------+-----------------+-------------------+-------+-------------+---------------+-------------+----------+----------------+----------+--------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net    |
+-----------+-----------------+-------------------+-------+-------------+---------------+-------------+----------+----------------+----------+--------+
| g1        | BUFG/O          | n/a               |       |             |               |         223 |        0 |              0 |        0 | T[3]_0 |
+-----------+-----------------+-------------------+-------+-------------+---------------+-------------+----------+----------------+----------+--------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+------+----+
|    | X0   | X1 |
+----+------+----+
| Y3 |    0 |  0 |
| Y2 |  223 |  0 |
| Y1 |    0 |  0 |
| Y0 |    0 |  0 |
+----+------+----+


9. Device Cell Placement Summary for Global Clock g2
----------------------------------------------------

+-----------+-----------------+-------------------+-------+-------------+---------------+-------------+----------+----------------+----------+--------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net    |
+-----------+-----------------+-------------------+-------+-------------+---------------+-------------+----------+----------------+----------+--------+
| g2        | BUFG/O          | n/a               |       |             |               |         200 |        0 |              0 |        0 | T[2]_1 |
+-----------+-----------------+-------------------+-------+-------------+---------------+-------------+----------+----------------+----------+--------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+------+----+
|    | X0   | X1 |
+----+------+----+
| Y3 |    0 |  0 |
| Y2 |  200 |  0 |
| Y1 |    0 |  0 |
| Y0 |    0 |  0 |
+----+------+----+


10. Device Cell Placement Summary for Global Clock g3
-----------------------------------------------------

+-----------+-----------------+-------------------+-------+-------------+---------------+-------------+----------+----------------+----------+--------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net    |
+-----------+-----------------+-------------------+-------+-------------+---------------+-------------+----------+----------------+----------+--------+
| g3        | BUFG/O          | n/a               |       |             |               |          47 |        0 |              0 |        0 | T[0]_2 |
+-----------+-----------------+-------------------+-------+-------------+---------------+-------------+----------+----------------+----------+--------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+-----+----+
|    | X0  | X1 |
+----+-----+----+
| Y3 |   0 |  0 |
| Y2 |  41 |  6 |
| Y1 |   0 |  0 |
| Y0 |   0 |  0 |
+----+-----+----+


11. Clock Region Cell Placement per Global Clock: Region X1Y1
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+-----------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net             |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+-----------------+
| g0        | n/a   | BUFG/O          | None       |           2 |               0 |  2 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | clock_IBUF_BUFG |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+-----------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


12. Clock Region Cell Placement per Global Clock: Region X0Y2
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+-----------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF  | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net             |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+-----------------+
| g0        | n/a   | BUFG/O          | None       |         343 |               0 | 343 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | clock_IBUF_BUFG |
| g1        | n/a   | BUFG/O          | None       |         223 |               0 | 223 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | T[3]_0          |
| g2        | n/a   | BUFG/O          | None       |         200 |               0 | 200 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | T[2]_1          |
| g3        | n/a   | BUFG/O          | None       |          41 |               0 |  41 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | T[0]_2          |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+-----------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


13. Clock Region Cell Placement per Global Clock: Region X1Y2
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+-----------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF  | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net             |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+-----------------+
| g0        | n/a   | BUFG/O          | None       |         138 |               0 | 138 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | clock_IBUF_BUFG |
| g3        | n/a   | BUFG/O          | None       |           6 |               0 |   6 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | T[0]_2          |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+-----------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


14. Clock Region Cell Placement per Global Clock: Region X0Y3
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+-----------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net             |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+-----------------+
| g0        | n/a   | BUFG/O          | None       |           2 |               0 |  2 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | clock_IBUF_BUFG |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+-----------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts



# Location of BUFG Primitives 
set_property LOC BUFGCTRL_X0Y0 [get_cells clock_IBUF_BUFG_inst]
set_property LOC BUFGCTRL_X0Y1 [get_cells display_code_reg[7][4]_i_3]
set_property LOC BUFGCTRL_X0Y2 [get_cells timer_data_reg[0][3]_i_3]
set_property LOC BUFGCTRL_X0Y3 [get_cells last_keyboard_reg[15]_i_1]

# Location of IO Primitives which is load of clock spine

# Location of clock ports
set_property LOC IOB_X0Y74 [get_ports clock]

# Clock net "clock_IBUF_BUFG" driven by instance "clock_IBUF_BUFG_inst" located at site "BUFGCTRL_X0Y0"
#startgroup
create_pblock {CLKAG_clock_IBUF_BUFG}
add_cells_to_pblock [get_pblocks  {CLKAG_clock_IBUF_BUFG}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="clock_IBUF_BUFG"}]]]
resize_pblock [get_pblocks {CLKAG_clock_IBUF_BUFG}] -add {CLOCKREGION_X0Y2:CLOCKREGION_X0Y2 CLOCKREGION_X0Y3:CLOCKREGION_X0Y3 CLOCKREGION_X1Y1:CLOCKREGION_X1Y1 CLOCKREGION_X1Y2:CLOCKREGION_X1Y2}
#endgroup

# Clock net "T[3]_0" driven by instance "display_code_reg[7][4]_i_3" located at site "BUFGCTRL_X0Y1"
#startgroup
create_pblock {CLKAG_T[3]_0}
add_cells_to_pblock [get_pblocks  {CLKAG_T[3]_0}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="T[3]_0"}]]]
resize_pblock [get_pblocks {CLKAG_T[3]_0}] -add {CLOCKREGION_X0Y2:CLOCKREGION_X0Y2}
#endgroup

# Clock net "T[2]_1" driven by instance "timer_data_reg[0][3]_i_3" located at site "BUFGCTRL_X0Y2"
#startgroup
create_pblock {CLKAG_T[2]_1}
add_cells_to_pblock [get_pblocks  {CLKAG_T[2]_1}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="T[2]_1"}]]]
resize_pblock [get_pblocks {CLKAG_T[2]_1}] -add {CLOCKREGION_X0Y2:CLOCKREGION_X0Y2}
#endgroup

# Clock net "T[0]_2" driven by instance "last_keyboard_reg[15]_i_1" located at site "BUFGCTRL_X0Y3"
#startgroup
create_pblock {CLKAG_T[0]_2}
add_cells_to_pblock [get_pblocks  {CLKAG_T[0]_2}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="T[0]_2"}]]]
resize_pblock [get_pblocks {CLKAG_T[0]_2}] -add {CLOCKREGION_X0Y2:CLOCKREGION_X0Y2 CLOCKREGION_X1Y2:CLOCKREGION_X1Y2}
#endgroup
