<?xml version="1.0" encoding="UTF-8"?>
<GenRun Id="impl_1" LaunchPart="xc7a100tfgg484-1" LaunchTime="1748961486">
  <File Type="BITSTR-BMM" Name="top_module_bd.bmm"/>
  <File Type="OPT-METHODOLOGY-DRC" Name="top_module_methodology_drc_opted.rpt"/>
  <File Type="INIT-TIMING" Name="top_module_timing_summary_init.rpt"/>
  <File Type="ROUTE-PWR" Name="top_module_power_routed.rpt"/>
  <File Type="PA-TCL" Name="top_module.tcl"/>
  <File Type="OPT-TIMING" Name="top_module_timing_summary_opted.rpt"/>
  <File Type="OPT-DCP" Name="top_module_opt.dcp"/>
  <File Type="ROUTE-PWR-SUM" Name="top_module_power_summary_routed.pb"/>
  <File Type="REPORTS-TCL" Name="top_module_reports.tcl"/>
  <File Type="OPT-DRC" Name="top_module_drc_opted.rpt"/>
  <File Type="OPT-HWDEF" Name="top_module.hwdef"/>
  <File Type="PWROPT-DCP" Name="top_module_pwropt.dcp"/>
  <File Type="PWROPT-DRC" Name="top_module_drc_pwropted.rpt"/>
  <File Type="PWROPT-TIMING" Name="top_module_timing_summary_pwropted.rpt"/>
  <File Type="PLACE-DCP" Name="top_module_placed.dcp"/>
  <File Type="PLACE-IO" Name="top_module_io_placed.rpt"/>
  <File Type="PLACE-CLK" Name="top_module_clock_utilization_placed.rpt"/>
  <File Type="PLACE-UTIL" Name="top_module_utilization_placed.rpt"/>
  <File Type="PLACE-UTIL-PB" Name="top_module_utilization_placed.pb"/>
  <File Type="PLACE-CTRL" Name="top_module_control_sets_placed.rpt"/>
  <File Type="PLACE-SIMILARITY" Name="top_module_incremental_reuse_placed.rpt"/>
  <File Type="PLACE-PRE-SIMILARITY" Name="top_module_incremental_reuse_pre_placed.rpt"/>
  <File Type="BG-BGN" Name="top_module.bgn"/>
  <File Type="PLACE-TIMING" Name="top_module_timing_summary_placed.rpt"/>
  <File Type="POSTPLACE-PWROPT-DCP" Name="top_module_postplace_pwropt.dcp"/>
  <File Type="BG-BIN" Name="top_module.bin"/>
  <File Type="POSTPLACE-PWROPT-TIMING" Name="top_module_timing_summary_postplace_pwropted.rpt"/>
  <File Type="PHYSOPT-DCP" Name="top_module_physopt.dcp"/>
  <File Type="PHYSOPT-DRC" Name="top_module_drc_physopted.rpt"/>
  <File Type="BITSTR-MSK" Name="top_module.msk"/>
  <File Type="PHYSOPT-TIMING" Name="top_module_timing_summary_physopted.rpt"/>
  <File Type="ROUTE-ERROR-DCP" Name="top_module_routed_error.dcp"/>
  <File Type="ROUTE-DCP" Name="top_module_routed.dcp"/>
  <File Type="ROUTE-BLACKBOX-DCP" Name="top_module_routed_bb.dcp"/>
  <File Type="ROUTE-DRC" Name="top_module_drc_routed.rpt"/>
  <File Type="ROUTE-DRC-PB" Name="top_module_drc_routed.pb"/>
  <File Type="BITSTR-LTX" Name="debug_nets.ltx"/>
  <File Type="BITSTR-LTX" Name="top_module.ltx"/>
  <File Type="ROUTE-DRC-RPX" Name="top_module_drc_routed.rpx"/>
  <File Type="BITSTR-MMI" Name="top_module.mmi"/>
  <File Type="ROUTE-METHODOLOGY-DRC" Name="top_module_methodology_drc_routed.rpt"/>
  <File Type="ROUTE-METHODOLOGY-DRC-RPX" Name="top_module_methodology_drc_routed.rpx"/>
  <File Type="BITSTR-SYSDEF" Name="top_module.sysdef"/>
  <File Type="ROUTE-METHODOLOGY-DRC-PB" Name="top_module_methodology_drc_routed.pb"/>
  <File Type="ROUTE-PWR-RPX" Name="top_module_power_routed.rpx"/>
  <File Type="ROUTE-STATUS" Name="top_module_route_status.rpt"/>
  <File Type="ROUTE-STATUS-PB" Name="top_module_route_status.pb"/>
  <File Type="ROUTE-TIMINGSUMMARY" Name="top_module_timing_summary_routed.rpt"/>
  <File Type="ROUTE-TIMING-PB" Name="top_module_timing_summary_routed.pb"/>
  <File Type="ROUTE-TIMING-RPX" Name="top_module_timing_summary_routed.rpx"/>
  <File Type="ROUTE-SIMILARITY" Name="top_module_incremental_reuse_routed.rpt"/>
  <File Type="ROUTE-CLK" Name="top_module_clock_utilization_routed.rpt"/>
  <File Type="ROUTE-BUS-SKEW" Name="top_module_bus_skew_routed.rpt"/>
  <File Type="ROUTE-BUS-SKEW-PB" Name="top_module_bus_skew_routed.pb"/>
  <File Type="ROUTE-BUS-SKEW-RPX" Name="top_module_bus_skew_routed.rpx"/>
  <File Type="POSTROUTE-PHYSOPT-DCP" Name="top_module_postroute_physopt.dcp"/>
  <File Type="POSTROUTE-PHYSOPT-BLACKBOX-DCP" Name="top_module_postroute_physopt_bb.dcp"/>
  <File Type="POSTROUTE-PHYSOPT-TIMING" Name="top_module_timing_summary_postroute_physopted.rpt"/>
  <File Type="POSTROUTE-PHYSOPT-TIMING-PB" Name="top_module_timing_summary_postroute_physopted.pb"/>
  <File Type="POSTROUTE-PHYSOPT-TIMING-RPX" Name="top_module_timing_summary_postroute_physopted.rpx"/>
  <File Type="POSTROUTE-PHYSOPT-BUS-SKEW" Name="top_module_bus_skew_postroute_physopted.rpt"/>
  <File Type="POSTROUTE-PHYSOPT-BUS-SKEW-PB" Name="top_module_bus_skew_postroute_physopted.pb"/>
  <File Type="BG-BIT" Name="top_module.bit"/>
  <File Type="POSTROUTE-PHYSOPT-BUS-SKEW-RPX" Name="top_module_bus_skew_postroute_physopted.rpx"/>
  <File Type="BITSTR-RBT" Name="top_module.rbt"/>
  <File Type="BITSTR-NKY" Name="top_module.nky"/>
  <File Type="BG-DRC" Name="top_module.drc"/>
  <File Type="RDI-RDI" Name="top_module.vdi"/>
  <File Type="WBT-USG" Name="usage_statistics_webtalk.html"/>
  <FileSet Name="sources" Type="DesignSrcs" RelSrcDir="$PSRCDIR/sources_1">
    <Filter Type="Srcs"/>
    <File Path="$PPRDIR/src/hdl/config.svh">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/config/system_config.svh">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/config/display_config.svh">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/config/input_config.svh">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/modules/audio_generator.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/controller/audio_output_generator.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/modules/buttons.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/controller/controller.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/controller/data_modifier.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/utils/debouncer.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/modules/digital_tube.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/controller/display_data_generator.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/utils/divider.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/controller/input_controller.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/modules/keyboards.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/utils/segment_decoder.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/utils/synchronizer.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/utils/timing.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/utils/v_divider.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PPRDIR/src/hdl/top_module.sv">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="DesignMode" Val="RTL"/>
      <Option Name="TopModule" Val="top_module"/>
      <Option Name="TopAutoSet" Val="TRUE"/>
    </Config>
  </FileSet>
  <FileSet Name="constrs_in" Type="Constrs" RelSrcDir="$PSRCDIR/constrs_1">
    <Filter Type="Constrs"/>
    <File Path="$PPRDIR/src/xdc/constraints.xdc">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="ConstrsType" Val="XDC"/>
    </Config>
  </FileSet>
  <FileSet Name="utils" Type="Utils" RelSrcDir="$PSRCDIR/utils_1">
    <Filter Type="Utils"/>
    <Config>
      <Option Name="TopAutoSet" Val="TRUE"/>
    </Config>
  </FileSet>
  <Strategy Version="1" Minor="2">
    <StratHandle Name="Vivado Implementation Defaults" Flow="Vivado Implementation 2018"/>
    <Step Id="init_design"/>
    <Step Id="opt_design"/>
    <Step Id="power_opt_design"/>
    <Step Id="place_design"/>
    <Step Id="post_place_power_opt_design"/>
    <Step Id="phys_opt_design"/>
    <Step Id="route_design"/>
    <Step Id="post_route_phys_opt_design"/>
    <Step Id="write_bitstream"/>
  </Strategy>
</GenRun>
