<HTML><HEAD><TITLE>Device Usage Statistics Report</TITLE></HEAD>
<BODY TEXT='#000000' BGCOLOR='#FFFFFF' LINK='#0000EE' VLINK='#551A8B' ALINK='#FF0000'><H3>Device Usage Page (usage_statistics_webtalk.html)</H3>This HTML page displays the device usage statistics that will be sent to Xilinx.<BR>To see the actual file transmitted to Xilinx, please click <A HREF="./usage_statistics_webtalk.xml">here</A>.<BR><BR><HR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>software_version_and_target_device</B></TD></TR>
<TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>beta</B></TD><TD>FALSE</TD>
  <TD BGCOLOR='#DBE5F1'><B>build_version</B></TD><TD>2405991</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>date_generated</B></TD><TD>Tue Jun  3 22:39:45 2025</TD>
  <TD BGCOLOR='#DBE5F1'><B>os_platform</B></TD><TD>WIN64</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>product_version</B></TD><TD>Vivado v2018.3 (64-bit)</TD>
  <TD BGCOLOR='#DBE5F1'><B>project_id</B></TD><TD>20ae3c0b38a74517b6e9d56f940a37da</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>project_iteration</B></TD><TD>14</TD>
  <TD BGCOLOR='#DBE5F1'><B>random_id</B></TD><TD>1df8074c817b5f1cb46b2114e3a8a019</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>registration_id</B></TD><TD>1df8074c817b5f1cb46b2114e3a8a019</TD>
  <TD BGCOLOR='#DBE5F1'><B>route_design</B></TD><TD>TRUE</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>target_device</B></TD><TD>xc7a100t</TD>
  <TD BGCOLOR='#DBE5F1'><B>target_family</B></TD><TD>artix7</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>target_package</B></TD><TD>fgg484</TD>
  <TD BGCOLOR='#DBE5F1'><B>target_speed</B></TD><TD>-1</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>tool_flow</B></TD><TD>Vivado</TD>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>user_environment</B></TD></TR>
<TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>cpu_name</B></TD><TD>12th Gen Intel(R) Core(TM) i7-12700H</TD>
  <TD BGCOLOR='#DBE5F1'><B>cpu_speed</B></TD><TD>2688 MHz</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>os_name</B></TD><TD>Microsoft Windows 8 or later , 64-bit</TD>
  <TD BGCOLOR='#DBE5F1'><B>os_release</B></TD><TD>major release  (build 9200)</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>system_ram</B></TD><TD>16.000 GB</TD>
  <TD BGCOLOR='#DBE5F1'><B>total_processors</B></TD><TD>1</TD>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>vivado_usage</B></TD></TR>
<TR ALIGN='LEFT'>  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>gui_handlers</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>abstractfileview_reload=2</TD>
   <TD>addsrcwizard_specify_or_create_constraint_files=1</TD>
   <TD>basedialog_ok=32</TD>
   <TD>basedialog_yes=16</TD>
</TR><TR ALIGN='LEFT'>   <TD>cmdmsgdialog_ok=3</TD>
   <TD>constraintschooserpanel_add_existing_or_create_new_constraints=1</TD>
   <TD>constraintschooserpanel_add_files=1</TD>
   <TD>filesetpanel_file_set_panel_tree=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>flownavigatortreepanel_flow_navigator_tree=45</TD>
   <TD>fpgachooser_fpga_table=3</TD>
   <TD>gettingstartedview_create_new_project=1</TD>
   <TD>mainmenumgr_file=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>mainmenumgr_project=1</TD>
   <TD>msgtreepanel_message_view_tree=4</TD>
   <TD>pacommandnames_add_sources=1</TD>
   <TD>pacommandnames_auto_connect_target=5</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_new_project=1</TD>
   <TD>paviews_project_summary=3</TD>
   <TD>programfpgadialog_program=14</TD>
   <TD>projectnamechooser_project_name=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>saveprojectutils_save=1</TD>
   <TD>srcchooserpanel_add_hdl_and_netlist_files_to_your_project=8</TD>
   <TD>srcchooserpanel_add_or_create_source_file=6</TD>
   <TD>syntheticagettingstartedview_recent_projects=1</TD>
</TR>  </TABLE>
  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>java_command_handlers</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>addsources=2</TD>
   <TD>autoconnecttarget=5</TD>
   <TD>launchprogramfpga=15</TD>
   <TD>newproject=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>openhardwaremanager=22</TD>
   <TD>openrecenttarget=7</TD>
   <TD>programdevice=17</TD>
   <TD>runbitgen=16</TD>
</TR><TR ALIGN='LEFT'>   <TD>showview=13</TD>
   <TD>viewtaskimplementation=2</TD>
</TR>  </TABLE>
</TR><TR ALIGN='LEFT'>  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>other_data</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>guimode=3</TD>
</TR>  </TABLE>
  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>project_data</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>constraintsetcount=1</TD>
   <TD>core_container=false</TD>
   <TD>currentimplrun=impl_1</TD>
   <TD>currentsynthesisrun=synth_1</TD>
</TR><TR ALIGN='LEFT'>   <TD>default_library=xil_defaultlib</TD>
   <TD>designmode=RTL</TD>
   <TD>export_simulation_activehdl=0</TD>
   <TD>export_simulation_ies=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>export_simulation_modelsim=0</TD>
   <TD>export_simulation_questa=0</TD>
   <TD>export_simulation_riviera=0</TD>
   <TD>export_simulation_vcs=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>export_simulation_xsim=0</TD>
   <TD>implstrategy=Vivado Implementation Defaults</TD>
   <TD>launch_simulation_activehdl=0</TD>
   <TD>launch_simulation_ies=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>launch_simulation_modelsim=0</TD>
   <TD>launch_simulation_questa=0</TD>
   <TD>launch_simulation_riviera=0</TD>
   <TD>launch_simulation_vcs=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>launch_simulation_xsim=0</TD>
   <TD>simulator_language=Mixed</TD>
   <TD>srcsetcount=20</TD>
   <TD>synthesisstrategy=Vivado Synthesis Defaults</TD>
</TR><TR ALIGN='LEFT'>   <TD>target_language=Verilog</TD>
   <TD>target_simulator=XSim</TD>
   <TD>totalimplruns=1</TD>
   <TD>totalsynthesisruns=1</TD>
</TR>  </TABLE>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>unisim_transformation</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>post_unisim_transformation</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufg=4</TD>
    <TD>carry4=193</TD>
    <TD>fdce=981</TD>
    <TD>gnd=26</TD>
</TR><TR ALIGN='LEFT'>    <TD>ibuf=11</TD>
    <TD>lut1=47</TD>
    <TD>lut2=314</TD>
    <TD>lut3=771</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut4=201</TD>
    <TD>lut5=324</TD>
    <TD>lut6=392</TD>
    <TD>muxf7=5</TD>
</TR><TR ALIGN='LEFT'>    <TD>obuf=21</TD>
    <TD>vcc=35</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>pre_unisim_transformation</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufg=4</TD>
    <TD>carry4=193</TD>
    <TD>fdce=981</TD>
    <TD>gnd=26</TD>
</TR><TR ALIGN='LEFT'>    <TD>ibuf=11</TD>
    <TD>lut1=47</TD>
    <TD>lut2=314</TD>
    <TD>lut3=771</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut4=201</TD>
    <TD>lut5=324</TD>
    <TD>lut6=392</TD>
    <TD>muxf7=5</TD>
</TR><TR ALIGN='LEFT'>    <TD>obuf=21</TD>
    <TD>vcc=35</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_drc</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-append=default::[not_specified]</TD>
    <TD>-checks=default::[not_specified]</TD>
    <TD>-fail_on=default::[not_specified]</TD>
    <TD>-force=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-format=default::[not_specified]</TD>
    <TD>-internal=default::[not_specified]</TD>
    <TD>-internal_only=default::[not_specified]</TD>
    <TD>-messages=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-name=default::[not_specified]</TD>
    <TD>-no_waivers=default::[not_specified]</TD>
    <TD>-return_string=default::[not_specified]</TD>
    <TD>-ruledecks=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-upgrade_cw=default::[not_specified]</TD>
    <TD>-waived=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>results</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>cfgbvs-1=1</TD>
    <TD>pdrc-153=1</TD>
    <TD>plholdvio-2=1</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_methodology</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-append=default::[not_specified]</TD>
    <TD>-checks=default::[not_specified]</TD>
    <TD>-fail_on=default::[not_specified]</TD>
    <TD>-force=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-format=default::[not_specified]</TD>
    <TD>-messages=default::[not_specified]</TD>
    <TD>-name=default::[not_specified]</TD>
    <TD>-return_string=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-slack_lesser_than=default::[not_specified]</TD>
    <TD>-waived=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>results</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>timing-16=1</TD>
    <TD>timing-17=496</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_power</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-advisory=default::[not_specified]</TD>
    <TD>-append=default::[not_specified]</TD>
    <TD>-file=[specified]</TD>
    <TD>-format=default::text</TD>
</TR><TR ALIGN='LEFT'>    <TD>-hier=default::power</TD>
    <TD>-hierarchical_depth=default::4</TD>
    <TD>-l=default::[not_specified]</TD>
    <TD>-name=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-no_propagation=default::[not_specified]</TD>
    <TD>-return_string=default::[not_specified]</TD>
    <TD>-rpx=[specified]</TD>
    <TD>-verbose=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-vid=default::[not_specified]</TD>
    <TD>-xpe=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>airflow=250 (LFM)</TD>
    <TD>ambient_temp=25.0 (C)</TD>
    <TD>bi-dir_toggle=12.500000</TD>
    <TD>bidir_output_enable=1.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>board_layers=12to15 (12 to 15 Layers)</TD>
    <TD>board_selection=medium (10&quot;x10&quot;)</TD>
    <TD>clocks=0.005281</TD>
    <TD>confidence_level_clock_activity=Low</TD>
</TR><TR ALIGN='LEFT'>    <TD>confidence_level_design_state=High</TD>
    <TD>confidence_level_device_models=High</TD>
    <TD>confidence_level_internal_activity=Medium</TD>
    <TD>confidence_level_io_activity=Low</TD>
</TR><TR ALIGN='LEFT'>    <TD>confidence_level_overall=Low</TD>
    <TD>customer=TBD</TD>
    <TD>customer_class=TBD</TD>
    <TD>devstatic=0.097116</TD>
</TR><TR ALIGN='LEFT'>    <TD>die=xc7a100tfgg484-1</TD>
    <TD>dsp_output_toggle=12.500000</TD>
    <TD>dynamic=0.062436</TD>
    <TD>effective_thetaja=2.7</TD>
</TR><TR ALIGN='LEFT'>    <TD>enable_probability=0.990000</TD>
    <TD>family=artix7</TD>
    <TD>ff_toggle=12.500000</TD>
    <TD>flow_state=routed</TD>
</TR><TR ALIGN='LEFT'>    <TD>heatsink=medium (Medium Profile)</TD>
    <TD>i/o=0.042972</TD>
    <TD>input_toggle=12.500000</TD>
    <TD>junction_temp=25.4 (C)</TD>
</TR><TR ALIGN='LEFT'>    <TD>logic=0.008487</TD>
    <TD>mgtavcc_dynamic_current=0.000000</TD>
    <TD>mgtavcc_static_current=0.000000</TD>
    <TD>mgtavcc_total_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>mgtavcc_voltage=1.000000</TD>
    <TD>mgtavtt_dynamic_current=0.000000</TD>
    <TD>mgtavtt_static_current=0.000000</TD>
    <TD>mgtavtt_total_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>mgtavtt_voltage=1.200000</TD>
    <TD>netlist_net_matched=NA</TD>
    <TD>off-chip_power=0.000000</TD>
    <TD>on-chip_power=0.159552</TD>
</TR><TR ALIGN='LEFT'>    <TD>output_enable=1.000000</TD>
    <TD>output_load=5.000000</TD>
    <TD>output_toggle=12.500000</TD>
    <TD>package=fgg484</TD>
</TR><TR ALIGN='LEFT'>    <TD>pct_clock_constrained=1.000000</TD>
    <TD>pct_inputs_defined=9</TD>
    <TD>platform=nt64</TD>
    <TD>process=typical</TD>
</TR><TR ALIGN='LEFT'>    <TD>ram_enable=50.000000</TD>
    <TD>ram_write=50.000000</TD>
    <TD>read_saif=False</TD>
    <TD>set/reset_probability=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>signal_rate=False</TD>
    <TD>signals=0.005697</TD>
    <TD>simulation_file=None</TD>
    <TD>speedgrade=-1</TD>
</TR><TR ALIGN='LEFT'>    <TD>static_prob=False</TD>
    <TD>temp_grade=commercial</TD>
    <TD>thetajb=6.8 (C/W)</TD>
    <TD>thetasa=4.6 (C/W)</TD>
</TR><TR ALIGN='LEFT'>    <TD>toggle_rate=False</TD>
    <TD>user_board_temp=25.0 (C)</TD>
    <TD>user_effective_thetaja=2.7</TD>
    <TD>user_junc_temp=25.4 (C)</TD>
</TR><TR ALIGN='LEFT'>    <TD>user_thetajb=6.8 (C/W)</TD>
    <TD>user_thetasa=4.6 (C/W)</TD>
    <TD>vccadc_dynamic_current=0.000000</TD>
    <TD>vccadc_static_current=0.020000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccadc_total_current=0.020000</TD>
    <TD>vccadc_voltage=1.800000</TD>
    <TD>vccaux_dynamic_current=0.001571</TD>
    <TD>vccaux_io_dynamic_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccaux_io_static_current=0.000000</TD>
    <TD>vccaux_io_total_current=0.000000</TD>
    <TD>vccaux_io_voltage=1.800000</TD>
    <TD>vccaux_static_current=0.018143</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccaux_total_current=0.019714</TD>
    <TD>vccaux_voltage=1.800000</TD>
    <TD>vccbram_dynamic_current=0.000000</TD>
    <TD>vccbram_static_current=0.000245</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccbram_total_current=0.000245</TD>
    <TD>vccbram_voltage=1.000000</TD>
    <TD>vccint_dynamic_current=0.019565</TD>
    <TD>vccint_static_current=0.015014</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccint_total_current=0.034579</TD>
    <TD>vccint_voltage=1.000000</TD>
    <TD>vcco12_dynamic_current=0.000000</TD>
    <TD>vcco12_static_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco12_total_current=0.000000</TD>
    <TD>vcco12_voltage=1.200000</TD>
    <TD>vcco135_dynamic_current=0.000000</TD>
    <TD>vcco135_static_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco135_total_current=0.000000</TD>
    <TD>vcco135_voltage=1.350000</TD>
    <TD>vcco15_dynamic_current=0.000000</TD>
    <TD>vcco15_static_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco15_total_current=0.000000</TD>
    <TD>vcco15_voltage=1.500000</TD>
    <TD>vcco18_dynamic_current=0.000000</TD>
    <TD>vcco18_static_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco18_total_current=0.000000</TD>
    <TD>vcco18_voltage=1.800000</TD>
    <TD>vcco25_dynamic_current=0.000000</TD>
    <TD>vcco25_static_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco25_total_current=0.000000</TD>
    <TD>vcco25_voltage=2.500000</TD>
    <TD>vcco33_dynamic_current=0.012134</TD>
    <TD>vcco33_static_current=0.004000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco33_total_current=0.016134</TD>
    <TD>vcco33_voltage=3.300000</TD>
    <TD>version=2018.3</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_utilization</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>clocking</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufgctrl_available=32</TD>
    <TD>bufgctrl_fixed=0</TD>
    <TD>bufgctrl_used=4</TD>
    <TD>bufgctrl_util_percentage=12.50</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufhce_available=96</TD>
    <TD>bufhce_fixed=0</TD>
    <TD>bufhce_used=0</TD>
    <TD>bufhce_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufio_available=24</TD>
    <TD>bufio_fixed=0</TD>
    <TD>bufio_used=0</TD>
    <TD>bufio_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufmrce_available=12</TD>
    <TD>bufmrce_fixed=0</TD>
    <TD>bufmrce_used=0</TD>
    <TD>bufmrce_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufr_available=24</TD>
    <TD>bufr_fixed=0</TD>
    <TD>bufr_used=0</TD>
    <TD>bufr_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>mmcme2_adv_available=6</TD>
    <TD>mmcme2_adv_fixed=0</TD>
    <TD>mmcme2_adv_used=0</TD>
    <TD>mmcme2_adv_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>plle2_adv_available=6</TD>
    <TD>plle2_adv_fixed=0</TD>
    <TD>plle2_adv_used=0</TD>
    <TD>plle2_adv_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>dsp</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>dsps_available=240</TD>
    <TD>dsps_fixed=0</TD>
    <TD>dsps_used=0</TD>
    <TD>dsps_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>io_standard</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>blvds_25=0</TD>
    <TD>diff_hstl_i=0</TD>
    <TD>diff_hstl_i_18=0</TD>
    <TD>diff_hstl_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_hstl_ii_18=0</TD>
    <TD>diff_hsul_12=0</TD>
    <TD>diff_mobile_ddr=0</TD>
    <TD>diff_sstl135=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl135_r=0</TD>
    <TD>diff_sstl15=0</TD>
    <TD>diff_sstl15_r=0</TD>
    <TD>diff_sstl18_i=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl18_ii=0</TD>
    <TD>hstl_i=0</TD>
    <TD>hstl_i_18=0</TD>
    <TD>hstl_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>hstl_ii_18=0</TD>
    <TD>hsul_12=0</TD>
    <TD>lvcmos12=0</TD>
    <TD>lvcmos15=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvcmos18=0</TD>
    <TD>lvcmos25=0</TD>
    <TD>lvcmos33=1</TD>
    <TD>lvds_25=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvttl=0</TD>
    <TD>mini_lvds_25=0</TD>
    <TD>mobile_ddr=0</TD>
    <TD>pci33_3=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>ppds_25=0</TD>
    <TD>rsds_25=0</TD>
    <TD>sstl135=0</TD>
    <TD>sstl135_r=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>sstl15=0</TD>
    <TD>sstl15_r=0</TD>
    <TD>sstl18_i=0</TD>
    <TD>sstl18_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>tmds_33=0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>memory</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>block_ram_tile_available=135</TD>
    <TD>block_ram_tile_fixed=0</TD>
    <TD>block_ram_tile_used=0</TD>
    <TD>block_ram_tile_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb18_available=270</TD>
    <TD>ramb18_fixed=0</TD>
    <TD>ramb18_used=0</TD>
    <TD>ramb18_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb36_fifo_available=135</TD>
    <TD>ramb36_fifo_fixed=0</TD>
    <TD>ramb36_fifo_used=0</TD>
    <TD>ramb36_fifo_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>primitives</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufg_functional_category=Clock</TD>
    <TD>bufg_used=4</TD>
    <TD>carry4_functional_category=CarryLogic</TD>
    <TD>carry4_used=193</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdce_functional_category=Flop &amp; Latch</TD>
    <TD>fdce_used=981</TD>
    <TD>ibuf_functional_category=IO</TD>
    <TD>ibuf_used=11</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut1_functional_category=LUT</TD>
    <TD>lut1_used=25</TD>
    <TD>lut2_functional_category=LUT</TD>
    <TD>lut2_used=314</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut3_functional_category=LUT</TD>
    <TD>lut3_used=771</TD>
    <TD>lut4_functional_category=LUT</TD>
    <TD>lut4_used=201</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut5_functional_category=LUT</TD>
    <TD>lut5_used=331</TD>
    <TD>lut6_functional_category=LUT</TD>
    <TD>lut6_used=385</TD>
</TR><TR ALIGN='LEFT'>    <TD>muxf7_functional_category=MuxFx</TD>
    <TD>muxf7_used=5</TD>
    <TD>obuf_functional_category=IO</TD>
    <TD>obuf_used=21</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>slice_logic</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>f7_muxes_available=31700</TD>
    <TD>f7_muxes_fixed=0</TD>
    <TD>f7_muxes_used=5</TD>
    <TD>f7_muxes_util_percentage=0.02</TD>
</TR><TR ALIGN='LEFT'>    <TD>f8_muxes_available=15850</TD>
    <TD>f8_muxes_fixed=0</TD>
    <TD>f8_muxes_used=0</TD>
    <TD>f8_muxes_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_logic_available=63400</TD>
    <TD>lut_as_logic_fixed=0</TD>
    <TD>lut_as_logic_used=1540</TD>
    <TD>lut_as_logic_util_percentage=2.43</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_memory_available=19000</TD>
    <TD>lut_as_memory_fixed=0</TD>
    <TD>lut_as_memory_used=0</TD>
    <TD>lut_as_memory_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_as_flip_flop_available=126800</TD>
    <TD>register_as_flip_flop_fixed=0</TD>
    <TD>register_as_flip_flop_used=981</TD>
    <TD>register_as_flip_flop_util_percentage=0.77</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_as_latch_available=126800</TD>
    <TD>register_as_latch_fixed=0</TD>
    <TD>register_as_latch_used=0</TD>
    <TD>register_as_latch_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_luts_available=63400</TD>
    <TD>slice_luts_fixed=0</TD>
    <TD>slice_luts_used=1540</TD>
    <TD>slice_luts_util_percentage=2.43</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_available=126800</TD>
    <TD>slice_registers_fixed=0</TD>
    <TD>slice_registers_used=981</TD>
    <TD>slice_registers_util_percentage=0.77</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_distributed_ram_fixed=0</TD>
    <TD>lut_as_distributed_ram_used=0</TD>
    <TD>lut_as_logic_available=63400</TD>
    <TD>lut_as_logic_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_logic_used=1540</TD>
    <TD>lut_as_logic_util_percentage=2.43</TD>
    <TD>lut_as_memory_available=19000</TD>
    <TD>lut_as_memory_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_memory_used=0</TD>
    <TD>lut_as_memory_util_percentage=0.00</TD>
    <TD>lut_as_shift_register_fixed=0</TD>
    <TD>lut_as_shift_register_used=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_in_front_of_the_register_is_unused_fixed=0</TD>
    <TD>lut_in_front_of_the_register_is_unused_used=52</TD>
    <TD>lut_in_front_of_the_register_is_used_fixed=52</TD>
    <TD>lut_in_front_of_the_register_is_used_used=126</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_driven_from_outside_the_slice_fixed=126</TD>
    <TD>register_driven_from_outside_the_slice_used=178</TD>
    <TD>register_driven_from_within_the_slice_fixed=178</TD>
    <TD>register_driven_from_within_the_slice_used=803</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_available=15850</TD>
    <TD>slice_fixed=0</TD>
    <TD>slice_registers_available=126800</TD>
    <TD>slice_registers_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_used=981</TD>
    <TD>slice_registers_util_percentage=0.77</TD>
    <TD>slice_used=484</TD>
    <TD>slice_util_percentage=3.05</TD>
</TR><TR ALIGN='LEFT'>    <TD>slicel_fixed=0</TD>
    <TD>slicel_used=333</TD>
    <TD>slicem_fixed=0</TD>
    <TD>slicem_used=151</TD>
</TR><TR ALIGN='LEFT'>    <TD>unique_control_sets_available=15850</TD>
    <TD>unique_control_sets_fixed=15850</TD>
    <TD>unique_control_sets_used=74</TD>
    <TD>unique_control_sets_util_percentage=0.47</TD>
</TR><TR ALIGN='LEFT'>    <TD>using_o5_and_o6_fixed=0.47</TD>
    <TD>using_o5_and_o6_used=487</TD>
    <TD>using_o5_output_only_fixed=487</TD>
    <TD>using_o5_output_only_used=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>using_o6_output_only_fixed=0</TD>
    <TD>using_o6_output_only_used=1053</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>specific_feature</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bscane2_available=4</TD>
    <TD>bscane2_fixed=0</TD>
    <TD>bscane2_used=0</TD>
    <TD>bscane2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>capturee2_available=1</TD>
    <TD>capturee2_fixed=0</TD>
    <TD>capturee2_used=0</TD>
    <TD>capturee2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>dna_port_available=1</TD>
    <TD>dna_port_fixed=0</TD>
    <TD>dna_port_used=0</TD>
    <TD>dna_port_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>efuse_usr_available=1</TD>
    <TD>efuse_usr_fixed=0</TD>
    <TD>efuse_usr_used=0</TD>
    <TD>efuse_usr_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>frame_ecce2_available=1</TD>
    <TD>frame_ecce2_fixed=0</TD>
    <TD>frame_ecce2_used=0</TD>
    <TD>frame_ecce2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>icape2_available=2</TD>
    <TD>icape2_fixed=0</TD>
    <TD>icape2_used=0</TD>
    <TD>icape2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcie_2_1_available=1</TD>
    <TD>pcie_2_1_fixed=0</TD>
    <TD>pcie_2_1_used=0</TD>
    <TD>pcie_2_1_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>startupe2_available=1</TD>
    <TD>startupe2_fixed=0</TD>
    <TD>startupe2_used=0</TD>
    <TD>startupe2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>xadc_available=1</TD>
    <TD>xadc_fixed=0</TD>
    <TD>xadc_used=0</TD>
    <TD>xadc_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>synthesis</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-assert=default::[not_specified]</TD>
    <TD>-bufg=default::12</TD>
    <TD>-cascade_dsp=default::auto</TD>
    <TD>-constrset=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-control_set_opt_threshold=default::auto</TD>
    <TD>-directive=default::default</TD>
    <TD>-fanout_limit=default::10000</TD>
    <TD>-flatten_hierarchy=default::rebuilt</TD>
</TR><TR ALIGN='LEFT'>    <TD>-fsm_extraction=default::auto</TD>
    <TD>-gated_clock_conversion=default::off</TD>
    <TD>-generic=default::[not_specified]</TD>
    <TD>-include_dirs=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-keep_equivalent_registers=default::[not_specified]</TD>
    <TD>-max_bram=default::-1</TD>
    <TD>-max_bram_cascade_height=default::-1</TD>
    <TD>-max_dsp=default::-1</TD>
</TR><TR ALIGN='LEFT'>    <TD>-max_uram=default::-1</TD>
    <TD>-max_uram_cascade_height=default::-1</TD>
    <TD>-mode=default::default</TD>
    <TD>-name=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-no_lc=default::[not_specified]</TD>
    <TD>-no_srlextract=default::[not_specified]</TD>
    <TD>-no_timing_driven=default::[not_specified]</TD>
    <TD>-part=xc7a100tfgg484-1</TD>
</TR><TR ALIGN='LEFT'>    <TD>-resource_sharing=default::auto</TD>
    <TD>-retiming=default::[not_specified]</TD>
    <TD>-rtl=default::[not_specified]</TD>
    <TD>-rtl_skip_constraints=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-rtl_skip_ip=default::[not_specified]</TD>
    <TD>-seu_protect=default::none</TD>
    <TD>-sfcu=default::[not_specified]</TD>
    <TD>-shreg_min_size=default::3</TD>
</TR><TR ALIGN='LEFT'>    <TD>-top=top_module</TD>
    <TD>-verilog_define=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>elapsed=00:00:29s</TD>
    <TD>hls_ip=0</TD>
    <TD>memory_gain=603.590MB</TD>
    <TD>memory_peak=924.137MB</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
</BODY>
</HTML>
