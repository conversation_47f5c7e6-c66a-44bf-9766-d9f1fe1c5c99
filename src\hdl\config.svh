`ifndef _GLOBAL_CONFIGURATION_H_
`define _GLOBAL_CONFIGURATION_H_

// ========================================
// 包含所有配置文件
// ========================================
`include "config/system_config.svh"  // 时钟频率、状态定义
`include "config/display_config.svh" // 字符编码、显示参数
`include "config/input_config.svh"   // 按键映射、键盘布局

// ========================================
// 向后兼容性定义
// ========================================
`define frequency `SYSTEM_FREQUENCY  // 系统时钟频率
`define frequency_fliping `DISPLAY_FLIP_FREQ  // 显示闪烁频率
`define audio_bps `AUDIO_BPS  // 音频节拍频率

`endif

