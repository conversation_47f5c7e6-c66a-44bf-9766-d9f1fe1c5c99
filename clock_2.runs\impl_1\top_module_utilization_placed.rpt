Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
-----------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Tue Jun  3 22:39:13 2025
| Host         : DESKTOP-5LO6MT6 running 64-bit major release  (build 9200)
| Command      : report_utilization -file top_module_utilization_placed.rpt -pb top_module_utilization_placed.pb
| Design       : top_module
| Device       : 7a100tfgg484-1
| Design State : Fully Placed
-----------------------------------------------------------------------------------------------------------------

Utilization Design Information

Table of Contents
-----------------
1. Slice Logic
1.1 Summary of Registers by Type
2. Slice Logic Distribution
3. Memory
4. DSP
5. IO and GT Specific
6. Clocking
7. Specific Feature
8. Primitives
9. Black Boxes
10. Instantiated Netlists

1. Slice Logic
--------------

+-------------------------+------+-------+-----------+-------+
|        Site Type        | Used | Fixed | Available | Util% |
+-------------------------+------+-------+-----------+-------+
| Slice LUTs              | 1540 |     0 |     63400 |  2.43 |
|   LUT as Logic          | 1540 |     0 |     63400 |  2.43 |
|   LUT as Memory         |    0 |     0 |     19000 |  0.00 |
| Slice Registers         |  981 |     0 |    126800 |  0.77 |
|   Register as Flip Flop |  981 |     0 |    126800 |  0.77 |
|   Register as Latch     |    0 |     0 |    126800 |  0.00 |
| F7 Muxes                |    5 |     0 |     31700 |  0.02 |
| F8 Muxes                |    0 |     0 |     15850 |  0.00 |
+-------------------------+------+-------+-----------+-------+


1.1 Summary of Registers by Type
--------------------------------

+-------+--------------+-------------+--------------+
| Total | Clock Enable | Synchronous | Asynchronous |
+-------+--------------+-------------+--------------+
| 0     |            _ |           - |            - |
| 0     |            _ |           - |          Set |
| 0     |            _ |           - |        Reset |
| 0     |            _ |         Set |            - |
| 0     |            _ |       Reset |            - |
| 0     |          Yes |           - |            - |
| 0     |          Yes |           - |          Set |
| 981   |          Yes |           - |        Reset |
| 0     |          Yes |         Set |            - |
| 0     |          Yes |       Reset |            - |
+-------+--------------+-------------+--------------+


2. Slice Logic Distribution
---------------------------

+--------------------------------------------+------+-------+-----------+-------+
|                  Site Type                 | Used | Fixed | Available | Util% |
+--------------------------------------------+------+-------+-----------+-------+
| Slice                                      |  484 |     0 |     15850 |  3.05 |
|   SLICEL                                   |  333 |     0 |           |       |
|   SLICEM                                   |  151 |     0 |           |       |
| LUT as Logic                               | 1540 |     0 |     63400 |  2.43 |
|   using O5 output only                     |    0 |       |           |       |
|   using O6 output only                     | 1053 |       |           |       |
|   using O5 and O6                          |  487 |       |           |       |
| LUT as Memory                              |    0 |     0 |     19000 |  0.00 |
|   LUT as Distributed RAM                   |    0 |     0 |           |       |
|   LUT as Shift Register                    |    0 |     0 |           |       |
| Slice Registers                            |  981 |     0 |    126800 |  0.77 |
|   Register driven from within the Slice    |  803 |       |           |       |
|   Register driven from outside the Slice   |  178 |       |           |       |
|     LUT in front of the register is unused |   52 |       |           |       |
|     LUT in front of the register is used   |  126 |       |           |       |
| Unique Control Sets                        |   74 |       |     15850 |  0.47 |
+--------------------------------------------+------+-------+-----------+-------+
* Note: Available Control Sets calculated as Slice Registers / 8, Review the Control Sets Report for more information regarding control sets.


3. Memory
---------

+----------------+------+-------+-----------+-------+
|    Site Type   | Used | Fixed | Available | Util% |
+----------------+------+-------+-----------+-------+
| Block RAM Tile |    0 |     0 |       135 |  0.00 |
|   RAMB36/FIFO* |    0 |     0 |       135 |  0.00 |
|   RAMB18       |    0 |     0 |       270 |  0.00 |
+----------------+------+-------+-----------+-------+
* Note: Each Block RAM Tile only has one FIFO logic available and therefore can accommodate only one FIFO36E1 or one FIFO18E1. However, if a FIFO18E1 occupies a Block RAM Tile, that tile can still accommodate a RAMB18E1


4. DSP
------

+-----------+------+-------+-----------+-------+
| Site Type | Used | Fixed | Available | Util% |
+-----------+------+-------+-----------+-------+
| DSPs      |    0 |     0 |       240 |  0.00 |
+-----------+------+-------+-----------+-------+


5. IO and GT Specific
---------------------

+-----------------------------+------+-------+-----------+-------+
|          Site Type          | Used | Fixed | Available | Util% |
+-----------------------------+------+-------+-----------+-------+
| Bonded IOB                  |   32 |    32 |       285 | 11.23 |
|   IOB Master Pads           |   16 |       |           |       |
|   IOB Slave Pads            |   14 |       |           |       |
| Bonded IPADs                |    0 |     0 |        14 |  0.00 |
| Bonded OPADs                |    0 |     0 |         8 |  0.00 |
| PHY_CONTROL                 |    0 |     0 |         6 |  0.00 |
| PHASER_REF                  |    0 |     0 |         6 |  0.00 |
| OUT_FIFO                    |    0 |     0 |        24 |  0.00 |
| IN_FIFO                     |    0 |     0 |        24 |  0.00 |
| IDELAYCTRL                  |    0 |     0 |         6 |  0.00 |
| IBUFDS                      |    0 |     0 |       274 |  0.00 |
| GTPE2_CHANNEL               |    0 |     0 |         4 |  0.00 |
| PHASER_OUT/PHASER_OUT_PHY   |    0 |     0 |        24 |  0.00 |
| PHASER_IN/PHASER_IN_PHY     |    0 |     0 |        24 |  0.00 |
| IDELAYE2/IDELAYE2_FINEDELAY |    0 |     0 |       300 |  0.00 |
| IBUFDS_GTE2                 |    0 |     0 |         2 |  0.00 |
| ILOGIC                      |    0 |     0 |       285 |  0.00 |
| OLOGIC                      |    0 |     0 |       285 |  0.00 |
+-----------------------------+------+-------+-----------+-------+


6. Clocking
-----------

+------------+------+-------+-----------+-------+
|  Site Type | Used | Fixed | Available | Util% |
+------------+------+-------+-----------+-------+
| BUFGCTRL   |    4 |     0 |        32 | 12.50 |
| BUFIO      |    0 |     0 |        24 |  0.00 |
| MMCME2_ADV |    0 |     0 |         6 |  0.00 |
| PLLE2_ADV  |    0 |     0 |         6 |  0.00 |
| BUFMRCE    |    0 |     0 |        12 |  0.00 |
| BUFHCE     |    0 |     0 |        96 |  0.00 |
| BUFR       |    0 |     0 |        24 |  0.00 |
+------------+------+-------+-----------+-------+


7. Specific Feature
-------------------

+-------------+------+-------+-----------+-------+
|  Site Type  | Used | Fixed | Available | Util% |
+-------------+------+-------+-----------+-------+
| BSCANE2     |    0 |     0 |         4 |  0.00 |
| CAPTUREE2   |    0 |     0 |         1 |  0.00 |
| DNA_PORT    |    0 |     0 |         1 |  0.00 |
| EFUSE_USR   |    0 |     0 |         1 |  0.00 |
| FRAME_ECCE2 |    0 |     0 |         1 |  0.00 |
| ICAPE2      |    0 |     0 |         2 |  0.00 |
| PCIE_2_1    |    0 |     0 |         1 |  0.00 |
| STARTUPE2   |    0 |     0 |         1 |  0.00 |
| XADC        |    0 |     0 |         1 |  0.00 |
+-------------+------+-------+-----------+-------+


8. Primitives
-------------

+----------+------+---------------------+
| Ref Name | Used | Functional Category |
+----------+------+---------------------+
| FDCE     |  981 |        Flop & Latch |
| LUT3     |  771 |                 LUT |
| LUT6     |  385 |                 LUT |
| LUT5     |  331 |                 LUT |
| LUT2     |  314 |                 LUT |
| LUT4     |  201 |                 LUT |
| CARRY4   |  193 |          CarryLogic |
| LUT1     |   25 |                 LUT |
| OBUF     |   21 |                  IO |
| IBUF     |   11 |                  IO |
| MUXF7    |    5 |               MuxFx |
| BUFG     |    4 |               Clock |
+----------+------+---------------------+


9. Black Boxes
--------------

+----------+------+
| Ref Name | Used |
+----------+------+


10. Instantiated Netlists
-------------------------

+----------+------+
| Ref Name | Used |
+----------+------+


