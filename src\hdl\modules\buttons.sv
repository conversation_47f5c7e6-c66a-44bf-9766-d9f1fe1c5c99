`include "config.svh"

parameter buttons_frequency = 100000000;
parameter buttons_debounce_ms = 10;
parameter buttons_number = 5;

module buttons (
    input logic clock,
    input logic reset_n,
    input logic [buttons_number - 1 : 0] raw_button,
    output logic [buttons_number - 1 : 0] button
);

    // 将异步的按键信号同步到系统时钟
    logic [buttons_number - 1 : 0] sync_button;
    synchronizer #(
        .width(buttons_number)
    ) sync_ins (
        .clock(clock),
        .reset_n(reset_n),
        .in(raw_button),
        .out(sync_button)
    );

    // 防抖处理
    // modulus = 100MHz / (1000/10) = 1,000,000，
    // 即10毫秒的时钟周期数
    generate
        for (genvar i = 0; i < buttons_number; i++) begin : debouncer_gen
            debouncer #(
                .frequency(buttons_frequency),
                .modulus  (buttons_frequency / (1000 / buttons_debounce_ms))
            ) deb_ins (
                .clock(clock),
                .reset_n(reset_n),
                .in(sync_button[i]),
                .out(button[i])
            );
        end
    endgenerate

endmodule
