`timescale 1ns / 1ps
`include "config.svh"

module top_module (
    // 系统时钟和复位
    input  logic        clock,                    // 系统时钟
    input  logic        reset,                    // 复位信号（高电平有效）

    // 输入设备
    input  logic [4:0]  raw_button,              // 5个按键输入
    input  logic [3:0]  keyboard_row_n,          // 4x4键盘行输入（低电平有效）
    output logic [3:0]  keyboard_col_n,          // 4x4键盘列输出（低电平有效）

    // 显示设备
    output logic [7:0]  digital_tube_enable_n,   // 8位数码管使能（低电平有效）
    output logic [6:0]  digital_tube_segment_n,  // 7段数码管段选（低电平有效）
    output logic        digital_tube_dp_n,       // 数码管小数点（低电平有效）

    // 音频设备
    output logic        buzzer_audio              // 蜂鸣器音频输出
);


    // 内部信号定义
    logic        reset_n;                        // 内部复位信号（低电平有效）
    logic [4:0]  button;                         // 防抖后的按键信号
    logic [15:0] keyboard;                       // 键盘扫描结果
    logic [7:0][5:0] display_code;              // 显示编码数据
    logic [31:0] frequency_select;               // 音频频率选择

    // 复位信号转换
    assign reset_n = ~reset;

    // 按键防抖模块
    buttons b_ins (
        .clock(clock),
        .reset_n(reset_n),
        .raw_button(raw_button),
        .button(button)
    );

    // 4x4矩阵键盘扫描模块
    keyboards k_ins (
        .clock(clock),
        .reset_n(reset_n),
        .keyboard_row_n(keyboard_row_n),
        .keyboard_col_n(keyboard_col_n),
        .keyboard(keyboard)
    );

    // 主控制器模块
    controller c_ins (
        .clock(clock),
        .reset_n(reset_n),
        .keyboard(keyboard),
        .button(button),
        .display_code(display_code),
        .frequency_select(frequency_select)
    );

    // 8位数码管显示驱动模块
    digital_tube dtb_ins (
        .clock(clock),
        .reset_n(reset_n),
        .data_code(display_code),
        .data_dp(8'b0),                          // 小数点全部关闭
        .enable_n(digital_tube_enable_n),
        .segment_n(digital_tube_segment_n),
        .dp_n(digital_tube_dp_n)
    );

    // 音频发生器模块
    audio_generator ag_ins (
        .clock(clock),
        .reset_n(reset_n),
        .frequency_select(frequency_select),
        .audio_output(buzzer_audio)
    );

endmodule
