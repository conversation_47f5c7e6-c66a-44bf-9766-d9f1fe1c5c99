`include "config.svh"

module input_controller (
    input logic clock,
    input logic reset_n,
    input logic [15:0] keyboard,
    input logic [4:0] button,
    output logic [15:0] keyboard_down,
    output logic [4:0] button_down,
    input logic h_add,
    input logic h_sub,
    input logic v_add,
    input logic v_sub,
    input state_t state,  // 添加状态输入，用于判断当前模式
    output logic [2:0] h_ptr,
    output logic [1:0] v_ptr
);

    // ========================================
    // 输入接收器部分 
    // ========================================
    
    logic [15:0] last_keyboard;
    always @(posedge clock or negedge reset_n) begin
        if (~reset_n) begin
            keyboard_down <= 'b0;
            last_keyboard <= 'b0;
        end else begin
            keyboard_down <= (~last_keyboard) & keyboard;
            last_keyboard <= keyboard;
        end
    end

    logic [4:0] last_button;
    always @(posedge clock or negedge reset_n) begin
        if (~reset_n) begin
            button_down <= 'b0;
            last_button <= 'b0;
        end else begin
            button_down <= (~last_button) & button;
            last_button <= button;
        end
    end

    // ========================================
    // 水平指针控制器部分
    // ========================================
    
    always @(posedge clock or negedge reset_n) begin
        if (~reset_n) begin
            h_ptr <= 'b0;
        end else begin
            if (h_add && h_sub) begin
                h_ptr <= h_ptr;  // 同时按下时保持不变
            end else if (h_add) begin
                h_ptr <= (h_ptr == `H_PTR_MAX - 1) ? 0 : h_ptr + 1;
            end else if (h_sub) begin
                h_ptr <= (h_ptr == 0) ? `H_PTR_MAX - 1 : h_ptr - 1;
            end
        end
    end

    // ========================================
    // 垂直指针控制器部分
    // ========================================

    always @(posedge clock or negedge reset_n) begin
        if (~reset_n) begin
            v_ptr <= 'b0;
        end else begin
            // 在倒计时模式下，S3和S5用于倒计时控制，不影响垂直指针
            if (state == countdown_setting_state) begin
                v_ptr <= v_ptr;  // 倒计时模式下保持垂直指针不变
            end else if (v_add && v_sub) begin
                v_ptr <= v_ptr;  // 同时按下时保持不变
            end else if (v_add) begin
                v_ptr <= (v_ptr == `V_PTR_MAX - 1) ? 0 : v_ptr + 1;
            end else if (v_sub) begin
                v_ptr <= (v_ptr == 0) ? `V_PTR_MAX - 1 : v_ptr - 1;
            end
        end
    end

endmodule
