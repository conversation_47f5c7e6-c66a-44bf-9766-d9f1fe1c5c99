#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Tue Jun  3 22:38:10 2025
# Process ID: 33692
# Current directory: E:/YS/clock_2/clock_2.runs/synth_1
# Command line: vivado.exe -log top_module.vds -product Vivado -mode batch -messageDb vivado.pb -notrace -source top_module.tcl
# Log file: E:/YS/clock_2/clock_2.runs/synth_1/top_module.vds
# Journal file: E:/YS/clock_2/clock_2.runs/synth_1\vivado.jou
#-----------------------------------------------------------
source top_module.tcl -notrace
Command: synth_design -top top_module -part xc7a100tfgg484-1
Starting synth_design
Attempting to get a license for feature 'Synthesis' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Synthesis' and/or device 'xc7a100t'
INFO: Launching helper process for spawning children vivado processes
INFO: Helper process launched with PID 21712 
WARNING: [Synth 8-992] state is already implicitly declared earlier [E:/YS/clock_2/src/hdl/controller/controller.sv:50]
---------------------------------------------------------------------------------
Starting RTL Elaboration : Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 443.363 ; gain = 111.328
---------------------------------------------------------------------------------
INFO: [Synth 8-6157] synthesizing module 'top_module' [E:/YS/clock_2/src/hdl/top_module.sv:4]
INFO: [Synth 8-6157] synthesizing module 'buttons' [E:/YS/clock_2/src/hdl/modules/buttons.sv:7]
INFO: [Synth 8-6157] synthesizing module 'debouncer' [E:/YS/clock_2/src/hdl/utils/debouncer.sv:1]
	Parameter frequency bound to: 100000000 - type: integer 
	Parameter modulus bound to: 1000000 - type: integer 
INFO: [Synth 8-6155] done synthesizing module 'debouncer' (1#1) [E:/YS/clock_2/src/hdl/utils/debouncer.sv:1]
INFO: [Synth 8-6157] synthesizing module 'synchronizer' [E:/YS/clock_2/src/hdl/utils/synchronizer.sv:1]
	Parameter width bound to: 5 - type: integer 
INFO: [Synth 8-6155] done synthesizing module 'synchronizer' (2#1) [E:/YS/clock_2/src/hdl/utils/synchronizer.sv:1]
INFO: [Synth 8-6155] done synthesizing module 'buttons' (3#1) [E:/YS/clock_2/src/hdl/modules/buttons.sv:7]
INFO: [Synth 8-6157] synthesizing module 'keyboards' [E:/YS/clock_2/src/hdl/modules/keyboards.sv:4]
INFO: [Synth 8-6157] synthesizing module 'debouncer__parameterized0' [E:/YS/clock_2/src/hdl/utils/debouncer.sv:1]
	Parameter frequency bound to: 2500000 - type: integer 
	Parameter modulus bound to: 25000 - type: integer 
INFO: [Synth 8-6155] done synthesizing module 'debouncer__parameterized0' (3#1) [E:/YS/clock_2/src/hdl/utils/debouncer.sv:1]
INFO: [Synth 8-6157] synthesizing module 'divider' [E:/YS/clock_2/src/hdl/utils/divider.sv:1]
	Parameter divisor bound to: 40 - type: integer 
INFO: [Synth 8-6155] done synthesizing module 'divider' (4#1) [E:/YS/clock_2/src/hdl/utils/divider.sv:1]
INFO: [Synth 8-6155] done synthesizing module 'keyboards' (5#1) [E:/YS/clock_2/src/hdl/modules/keyboards.sv:4]
INFO: [Synth 8-6157] synthesizing module 'controller' [E:/YS/clock_2/src/hdl/controller/controller.sv:3]
INFO: [Synth 8-6157] synthesizing module 'timing' [E:/YS/clock_2/src/hdl/utils/timing.sv:1]
	Parameter timing_count bound to: 4 - type: integer 
INFO: [Synth 8-6155] done synthesizing module 'timing' (6#1) [E:/YS/clock_2/src/hdl/utils/timing.sv:1]
INFO: [Synth 8-6157] synthesizing module 'input_controller' [E:/YS/clock_2/src/hdl/controller/input_controller.sv:3]
INFO: [Synth 8-6155] done synthesizing module 'input_controller' (7#1) [E:/YS/clock_2/src/hdl/controller/input_controller.sv:3]
INFO: [Synth 8-6157] synthesizing module 'data_modifier' [E:/YS/clock_2/src/hdl/controller/data_modifier.sv:3]
	Parameter ms_count bound to: 25000000 - type: integer 
WARNING: [Synth 8-5856] 3D RAM alarm_data_reg  for this pattern/configuration is not supported. This will most likely be implemented in registers
WARNING: [Synth 8-6014] Unused sequential element new_countdown_data_reg[5] was removed.  [E:/YS/clock_2/src/hdl/controller/data_modifier.sv:306]
WARNING: [Synth 8-6014] Unused sequential element new_countdown_data_reg[4] was removed.  [E:/YS/clock_2/src/hdl/controller/data_modifier.sv:306]
WARNING: [Synth 8-6014] Unused sequential element new_countdown_data_reg[3] was removed.  [E:/YS/clock_2/src/hdl/controller/data_modifier.sv:306]
WARNING: [Synth 8-6014] Unused sequential element new_countdown_data_reg[2] was removed.  [E:/YS/clock_2/src/hdl/controller/data_modifier.sv:306]
WARNING: [Synth 8-6014] Unused sequential element new_countdown_data_reg[1] was removed.  [E:/YS/clock_2/src/hdl/controller/data_modifier.sv:306]
WARNING: [Synth 8-6014] Unused sequential element new_countdown_data_reg[0] was removed.  [E:/YS/clock_2/src/hdl/controller/data_modifier.sv:306]
INFO: [Synth 8-6155] done synthesizing module 'data_modifier' (8#1) [E:/YS/clock_2/src/hdl/controller/data_modifier.sv:3]
INFO: [Synth 8-6157] synthesizing module 'display_data_generator' [E:/YS/clock_2/src/hdl/controller/display_data_generator.sv:3]
	Parameter divisor bound to: 6250000 - type: integer 
INFO: [Synth 8-6157] synthesizing module 'divider__parameterized0' [E:/YS/clock_2/src/hdl/utils/divider.sv:1]
	Parameter divisor bound to: 6250000 - type: integer 
INFO: [Synth 8-6155] done synthesizing module 'divider__parameterized0' (8#1) [E:/YS/clock_2/src/hdl/utils/divider.sv:1]
INFO: [Synth 8-6155] done synthesizing module 'display_data_generator' (9#1) [E:/YS/clock_2/src/hdl/controller/display_data_generator.sv:3]
INFO: [Synth 8-6157] synthesizing module 'audio_output_generator' [E:/YS/clock_2/src/hdl/controller/audio_output_generator.sv:3]
	Parameter ticking bound to: 6250000 - type: integer 
INFO: [Synth 8-155] case statement is not full and has no default [E:/YS/clock_2/src/hdl/controller/audio_output_generator.sv:86]
INFO: [Synth 8-155] case statement is not full and has no default [E:/YS/clock_2/src/hdl/controller/audio_output_generator.sv:152]
INFO: [Synth 8-6155] done synthesizing module 'audio_output_generator' (10#1) [E:/YS/clock_2/src/hdl/controller/audio_output_generator.sv:3]
INFO: [Synth 8-6155] done synthesizing module 'controller' (11#1) [E:/YS/clock_2/src/hdl/controller/controller.sv:3]
INFO: [Synth 8-6157] synthesizing module 'digital_tube' [E:/YS/clock_2/src/hdl/modules/digital_tube.sv:7]
INFO: [Synth 8-6157] synthesizing module 'divider__parameterized1' [E:/YS/clock_2/src/hdl/utils/divider.sv:1]
	Parameter divisor bound to: 125000 - type: integer 
INFO: [Synth 8-6155] done synthesizing module 'divider__parameterized1' (11#1) [E:/YS/clock_2/src/hdl/utils/divider.sv:1]
INFO: [Synth 8-6157] synthesizing module 'segment_decoder' [E:/YS/clock_2/src/hdl/utils/segment_decoder.sv:1]
INFO: [Synth 8-6155] done synthesizing module 'segment_decoder' (12#1) [E:/YS/clock_2/src/hdl/utils/segment_decoder.sv:1]
INFO: [Synth 8-6155] done synthesizing module 'digital_tube' (13#1) [E:/YS/clock_2/src/hdl/modules/digital_tube.sv:7]
INFO: [Synth 8-6157] synthesizing module 'audio_generator' [E:/YS/clock_2/src/hdl/modules/audio_generator.sv:45]
INFO: [Synth 8-6157] synthesizing module 'v_divider' [E:/YS/clock_2/src/hdl/utils/v_divider.sv:1]
	Parameter width bound to: 32 - type: integer 
INFO: [Synth 8-6155] done synthesizing module 'v_divider' (14#1) [E:/YS/clock_2/src/hdl/utils/v_divider.sv:1]
INFO: [Synth 8-6155] done synthesizing module 'audio_generator' (15#1) [E:/YS/clock_2/src/hdl/modules/audio_generator.sv:45]
INFO: [Synth 8-6155] done synthesizing module 'top_module' (16#1) [E:/YS/clock_2/src/hdl/top_module.sv:4]
WARNING: [Synth 8-3331] design data_modifier has unconnected port button_down[3]
---------------------------------------------------------------------------------
Finished RTL Elaboration : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 495.699 ; gain = 163.664
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 495.699 ; gain = 163.664
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 1 : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 495.699 ; gain = 163.664
---------------------------------------------------------------------------------
INFO: [Device 21-403] Loading part xc7a100tfgg484-1
INFO: [Project 1-570] Preparing netlist for logic optimization

Processing XDC Constraints
Initializing timing engine
Parsing XDC File [E:/YS/clock_2/src/xdc/constraints.xdc]
Finished Parsing XDC File [E:/YS/clock_2/src/xdc/constraints.xdc]
INFO: [Project 1-236] Implementation specific constraints were found while reading constraint file [E:/YS/clock_2/src/xdc/constraints.xdc]. These constraints will be ignored for synthesis but will be used in implementation. Impacted constraints are listed in the file [.Xil/top_module_propImpl.xdc].
Resolution: To avoid this warning, move constraints listed in [.Xil/top_module_propImpl.xdc] to another XDC file and exclude this new file from synthesis with the used_in_synthesis property (File Properties dialog in GUI) and re-run elaboration/synthesis.
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 858.039 ; gain = 0.000
Completed Processing XDC Constraints

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 858.082 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 858.082 ; gain = 0.000
Constraint Validation Runtime : Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.003 . Memory (MB): peak = 858.082 ; gain = 0.000
---------------------------------------------------------------------------------
Finished Constraint Validation : Time (s): cpu = 00:00:06 ; elapsed = 00:00:08 . Memory (MB): peak = 858.082 ; gain = 526.047
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Loading Part and Timing Information
---------------------------------------------------------------------------------
Loading part: xc7a100tfgg484-1
---------------------------------------------------------------------------------
Finished Loading Part and Timing Information : Time (s): cpu = 00:00:06 ; elapsed = 00:00:08 . Memory (MB): peak = 858.082 ; gain = 526.047
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Applying 'set_property' XDC Constraints
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished applying 'set_property' XDC Constraints : Time (s): cpu = 00:00:06 ; elapsed = 00:00:08 . Memory (MB): peak = 858.082 ; gain = 526.047
---------------------------------------------------------------------------------
INFO: [Synth 8-802] inferred FSM for state register 'column_scanning_reg' in module 'keyboards'
INFO: [Synth 8-5544] ROM "inner_keyboard" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "column_scanning" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "time_data_reg[5]" won't be mapped to Block RAM because address size (3) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "time_data_reg[4]" won't be mapped to Block RAM because address size (3) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "time_data_reg[3]" won't be mapped to Block RAM because address size (3) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "time_data_reg[2]" won't be mapped to Block RAM because address size (3) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "time_data_reg[1]" won't be mapped to Block RAM because address size (3) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "time_data_reg[0]" won't be mapped to Block RAM because address size (3) smaller than threshold (5)
INFO: [Synth 8-5546] ROM "countdown_finished" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5544] ROM "keyboard_warning" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "result" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "result" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "result" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "result" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "result" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "result" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "result" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "result" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "result" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "result" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5546] ROM "decrement_time1" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5544] ROM "result" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "result" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5546] ROM "alarm_music_data" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5544] ROM "oclock_music_data" won't be mapped to Block RAM because address size (3) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "warning_music_data" won't be mapped to Block RAM because address size (1) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "countdown_music_data" won't be mapped to Block RAM because address size (3) smaller than threshold (5)
WARNING: [Synth 8-6014] Unused sequential element alarm_address_reg_rep was removed.  [E:/YS/clock_2/src/hdl/controller/audio_output_generator.sv:78]
---------------------------------------------------------------------------------------------------
                   State |                     New Encoding |                Previous Encoding 
---------------------------------------------------------------------------------------------------
                 iSTATE3 |                            00001 |                             0000
*
                  iSTATE |                            00010 |                             1110
                 iSTATE0 |                            00100 |                             1101
                 iSTATE1 |                            01000 |                             1011
                 iSTATE2 |                            10000 |                             0111
---------------------------------------------------------------------------------------------------
INFO: [Synth 8-3354] encoded FSM with state register 'column_scanning_reg' using encoding 'one-hot' in module 'keyboards'
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 2 : Time (s): cpu = 00:00:08 ; elapsed = 00:00:09 . Memory (MB): peak = 858.082 ; gain = 526.047
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start RTL Component Statistics 
---------------------------------------------------------------------------------
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     32 Bit       Adders := 6     
	   2 Input     25 Bit       Adders := 1     
	   2 Input     23 Bit       Adders := 1     
	   2 Input      6 Bit       Adders := 1     
	  16 Input      5 Bit       Adders := 1     
	   2 Input      4 Bit       Adders := 18    
	   2 Input      3 Bit       Adders := 3     
	   2 Input      2 Bit       Adders := 3     
+---Registers : 
	               32 Bit    Registers := 2     
	               25 Bit    Registers := 1     
	               23 Bit    Registers := 1     
	               16 Bit    Registers := 2     
	                6 Bit    Registers := 9     
	                5 Bit    Registers := 4     
	                4 Bit    Registers := 47    
	                3 Bit    Registers := 3     
	                2 Bit    Registers := 2     
	                1 Bit    Registers := 57    
+---Muxes : 
	   2 Input     32 Bit        Muxes := 29    
	   5 Input     32 Bit        Muxes := 5     
	   9 Input     32 Bit        Muxes := 2     
	   3 Input     32 Bit        Muxes := 1     
	  20 Input     32 Bit        Muxes := 1     
	   2 Input     25 Bit        Muxes := 3     
	   2 Input     23 Bit        Muxes := 6     
	   2 Input      6 Bit        Muxes := 24    
	   6 Input      6 Bit        Muxes := 1     
	   5 Input      5 Bit        Muxes := 1     
	   5 Input      4 Bit        Muxes := 1     
	   2 Input      4 Bit        Muxes := 154   
	   4 Input      4 Bit        Muxes := 8     
	   2 Input      3 Bit        Muxes := 7     
	   3 Input      3 Bit        Muxes := 1     
	   2 Input      2 Bit        Muxes := 4     
	   7 Input      2 Bit        Muxes := 1     
	   2 Input      1 Bit        Muxes := 251   
	   8 Input      1 Bit        Muxes := 2     
	   3 Input      1 Bit        Muxes := 1     
	   5 Input      1 Bit        Muxes := 4     
---------------------------------------------------------------------------------
Finished RTL Component Statistics 
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start RTL Hierarchical Component Statistics 
---------------------------------------------------------------------------------
Hierarchical RTL Component report 
Module synchronizer 
Detailed RTL Component Info : 
+---Registers : 
	                5 Bit    Registers := 2     
Module debouncer 
Detailed RTL Component Info : 
+---Registers : 
	                1 Bit    Registers := 2     
+---Muxes : 
	   2 Input      1 Bit        Muxes := 1     
Module divider 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input      6 Bit       Adders := 1     
+---Registers : 
	                6 Bit    Registers := 1     
	                1 Bit    Registers := 1     
+---Muxes : 
	   2 Input      6 Bit        Muxes := 1     
Module debouncer__parameterized0 
Detailed RTL Component Info : 
+---Registers : 
	                1 Bit    Registers := 2     
+---Muxes : 
	   2 Input      1 Bit        Muxes := 1     
Module keyboards 
Detailed RTL Component Info : 
+---Registers : 
	                4 Bit    Registers := 4     
+---Muxes : 
	   5 Input      5 Bit        Muxes := 1     
	   5 Input      4 Bit        Muxes := 1     
Module timing 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input      2 Bit       Adders := 1     
+---Registers : 
	                2 Bit    Registers := 1     
+---Muxes : 
	   2 Input      2 Bit        Muxes := 1     
Module input_controller 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input      3 Bit       Adders := 2     
	   2 Input      2 Bit       Adders := 2     
+---Registers : 
	               16 Bit    Registers := 2     
	                5 Bit    Registers := 2     
	                3 Bit    Registers := 1     
	                2 Bit    Registers := 1     
+---Muxes : 
	   2 Input      3 Bit        Muxes := 3     
	   2 Input      2 Bit        Muxes := 3     
	   2 Input      1 Bit        Muxes := 3     
Module data_modifier 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     25 Bit       Adders := 1     
	  16 Input      5 Bit       Adders := 1     
	   2 Input      4 Bit       Adders := 18    
+---Registers : 
	               25 Bit    Registers := 1     
	                4 Bit    Registers := 43    
	                1 Bit    Registers := 6     
+---Muxes : 
	   2 Input     25 Bit        Muxes := 3     
	   2 Input      4 Bit        Muxes := 154   
	   4 Input      4 Bit        Muxes := 2     
	   3 Input      3 Bit        Muxes := 1     
	   2 Input      3 Bit        Muxes := 2     
	   2 Input      1 Bit        Muxes := 216   
	   8 Input      1 Bit        Muxes := 2     
	   3 Input      1 Bit        Muxes := 1     
Module divider__parameterized0 
Detailed RTL Component Info : 
+---Registers : 
	                1 Bit    Registers := 1     
Module display_data_generator 
Detailed RTL Component Info : 
+---Registers : 
	                6 Bit    Registers := 8     
+---Muxes : 
	   2 Input      6 Bit        Muxes := 22    
	   6 Input      6 Bit        Muxes := 1     
	   4 Input      4 Bit        Muxes := 6     
Module audio_output_generator 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     32 Bit       Adders := 4     
	   2 Input     23 Bit       Adders := 1     
+---Registers : 
	               32 Bit    Registers := 1     
	               23 Bit    Registers := 1     
	                1 Bit    Registers := 4     
+---Muxes : 
	   2 Input     32 Bit        Muxes := 28    
	   5 Input     32 Bit        Muxes := 5     
	   9 Input     32 Bit        Muxes := 2     
	   3 Input     32 Bit        Muxes := 1     
	  20 Input     32 Bit        Muxes := 1     
	   2 Input     23 Bit        Muxes := 6     
	   5 Input      1 Bit        Muxes := 4     
	   2 Input      1 Bit        Muxes := 8     
Module controller 
Detailed RTL Component Info : 
+---Registers : 
	                3 Bit    Registers := 1     
+---Muxes : 
	   2 Input      3 Bit        Muxes := 1     
	   7 Input      2 Bit        Muxes := 1     
	   2 Input      1 Bit        Muxes := 2     
Module divider__parameterized1 
Detailed RTL Component Info : 
+---Registers : 
	                1 Bit    Registers := 1     
Module digital_tube 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input      3 Bit       Adders := 1     
+---Registers : 
	                3 Bit    Registers := 1     
+---Muxes : 
	   2 Input      3 Bit        Muxes := 1     
Module v_divider 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     32 Bit       Adders := 2     
+---Registers : 
	               32 Bit    Registers := 1     
	                1 Bit    Registers := 2     
+---Muxes : 
	   2 Input     32 Bit        Muxes := 1     
	   2 Input      1 Bit        Muxes := 1     
Module audio_generator 
Detailed RTL Component Info : 
+---Muxes : 
	   2 Input      6 Bit        Muxes := 1     
---------------------------------------------------------------------------------
Finished RTL Hierarchical Component Statistics
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Part Resource Summary
---------------------------------------------------------------------------------
Part Resources:
DSPs: 240 (col length:80)
BRAMs: 270 (col length: RAMB18 80 RAMB36 40)
---------------------------------------------------------------------------------
Finished Part Resource Summary
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Cross Boundary and Area Optimization
---------------------------------------------------------------------------------
Warning: Parallel synthesis criteria is not met 
INFO: [Synth 8-5546] ROM "decrement_time1" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "countdown_finished" won't be mapped to RAM because it is too sparse
WARNING: [Synth 8-3917] design top_module has port digital_tube_dp_n driven by constant 1
WARNING: [Synth 8-3331] design data_modifier has unconnected port button_down[3]
INFO: [Synth 8-3886] merging instance 'c_ins/ddg_ins/display_code_reg[7][5]' (FDCE) to 'c_ins/ddg_ins/display_code_reg[7][4]'
INFO: [Synth 8-3886] merging instance 'c_ins/ddg_ins/display_code_reg[6][5]' (FDCE) to 'c_ins/ddg_ins/display_code_reg[6][4]'
INFO: [Synth 8-3886] merging instance 'c_ins/ddg_ins/display_code_reg[5][5]' (FDCE) to 'c_ins/ddg_ins/display_code_reg[5][4]'
INFO: [Synth 8-3886] merging instance 'c_ins/ddg_ins/display_code_reg[4][5]' (FDCE) to 'c_ins/ddg_ins/display_code_reg[4][4]'
INFO: [Synth 8-3886] merging instance 'c_ins/ddg_ins/display_code_reg[3][5]' (FDCE) to 'c_ins/ddg_ins/display_code_reg[3][4]'
INFO: [Synth 8-3886] merging instance 'c_ins/ddg_ins/display_code_reg[2][5]' (FDCE) to 'c_ins/ddg_ins/display_code_reg[2][4]'
INFO: [Synth 8-3886] merging instance 'c_ins/ddg_ins/display_code_reg[1][5]' (FDCE) to 'c_ins/ddg_ins/display_code_reg[1][4]'
INFO: [Synth 8-3886] merging instance 'c_ins/ddg_ins/display_code_reg[0][5]' (FDCE) to 'c_ins/ddg_ins/display_code_reg[0][4]'
INFO: [Synth 8-3886] merging instance 'c_ins/ddg_ins/display_code_reg[7][3]' (FDCE) to 'c_ins/ddg_ins/display_code_reg[7][1]'
WARNING: [Synth 8-3332] Sequential element (k_ins/FSM_onehot_column_scanning_reg[0]) is unused and will be removed from module top_module.
---------------------------------------------------------------------------------
Finished Cross Boundary and Area Optimization : Time (s): cpu = 00:00:17 ; elapsed = 00:00:19 . Memory (MB): peak = 858.082 ; gain = 526.047
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

ROM:
+----------------+--------------------------+---------------+----------------+
|Module Name     | RTL Object               | Depth x Width | Implemented As | 
+----------------+--------------------------+---------------+----------------+
|segment_decoder | segment_n                | 64x7          | LUT            | 
|audio_generator | note_divisor             | 64x19         | LUT            | 
|top_module      | dtb_ins/sd_ins/segment_n | 64x7          | LUT            | 
|top_module      | ag_ins/note_divisor      | 64x19         | LUT            | 
+----------------+--------------------------+---------------+----------------+

---------------------------------------------------------------------------------
Finished ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Applying XDC Timing Constraints
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Applying XDC Timing Constraints : Time (s): cpu = 00:00:22 ; elapsed = 00:00:24 . Memory (MB): peak = 858.082 ; gain = 526.047
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Timing Optimization
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Timing Optimization : Time (s): cpu = 00:00:23 ; elapsed = 00:00:26 . Memory (MB): peak = 905.375 ; gain = 573.340
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Technology Mapping
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Technology Mapping : Time (s): cpu = 00:00:24 ; elapsed = 00:00:27 . Memory (MB): peak = 924.137 ; gain = 592.102
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished IO Insertion : Time (s): cpu = 00:00:24 ; elapsed = 00:00:27 . Memory (MB): peak = 924.137 ; gain = 592.102
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Renaming Generated Instances
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Instances : Time (s): cpu = 00:00:24 ; elapsed = 00:00:27 . Memory (MB): peak = 924.137 ; gain = 592.102
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Rebuilding User Hierarchy
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Rebuilding User Hierarchy : Time (s): cpu = 00:00:24 ; elapsed = 00:00:27 . Memory (MB): peak = 924.137 ; gain = 592.102
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Ports
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Ports : Time (s): cpu = 00:00:24 ; elapsed = 00:00:27 . Memory (MB): peak = 924.137 ; gain = 592.102
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:24 ; elapsed = 00:00:27 . Memory (MB): peak = 924.137 ; gain = 592.102
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Nets
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Nets : Time (s): cpu = 00:00:24 ; elapsed = 00:00:27 . Memory (MB): peak = 924.137 ; gain = 592.102
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Writing Synthesis Report
---------------------------------------------------------------------------------

Report BlackBoxes: 
+-+--------------+----------+
| |BlackBox name |Instances |
+-+--------------+----------+
+-+--------------+----------+

Report Cell Usage: 
+------+-------+------+
|      |Cell   |Count |
+------+-------+------+
|1     |BUFG   |     4|
|2     |CARRY4 |   193|
|3     |LUT1   |    47|
|4     |LUT2   |   314|
|5     |LUT3   |   771|
|6     |LUT4   |   201|
|7     |LUT5   |   324|
|8     |LUT6   |   392|
|9     |MUXF7  |     5|
|10    |FDCE   |   981|
|11    |IBUF   |    11|
|12    |OBUF   |    21|
+------+-------+------+

Report Instance Areas: 
+------+------------------------------------------+-----------------------------+------+
|      |Instance                                  |Module                       |Cells |
+------+------------------------------------------+-----------------------------+------+
|1     |top                                       |                             |  3264|
|2     |  ag_ins                                  |audio_generator              |   232|
|3     |    vdiv_ins                              |v_divider                    |   232|
|4     |  b_ins                                   |buttons                      |   380|
|5     |    \debouncer_gen[0].deb_ins             |debouncer                    |    74|
|6     |    \debouncer_gen[1].deb_ins             |debouncer_15                 |    74|
|7     |    \debouncer_gen[2].deb_ins             |debouncer_16                 |    74|
|8     |    \debouncer_gen[3].deb_ins             |debouncer_17                 |    74|
|9     |    \debouncer_gen[4].deb_ins             |debouncer_18                 |    74|
|10    |    sync_ins                              |synchronizer                 |    10|
|11    |  c_ins                                   |controller                   |  1609|
|12    |    aog_ins                               |audio_output_generator       |   511|
|13    |    ddg_ins                               |display_data_generator       |   131|
|14    |      flip_ins                            |divider__parameterized0      |    66|
|15    |    dm_ins                                |data_modifier                |   729|
|16    |    ic_ins                                |input_controller             |   219|
|17    |    timer                                 |timing                       |     8|
|18    |  dtb_ins                                 |digital_tube                 |    66|
|19    |    dtb_divider                           |divider__parameterized1      |    52|
|20    |  k_ins                                   |keyboards                    |   940|
|21    |    \genblk1[0].debouncer_gen[0].deb_ins  |debouncer__parameterized0    |    56|
|22    |    \genblk1[0].debouncer_gen[1].deb_ins  |debouncer__parameterized0_0  |    56|
|23    |    \genblk1[0].debouncer_gen[2].deb_ins  |debouncer__parameterized0_1  |    56|
|24    |    \genblk1[0].debouncer_gen[3].deb_ins  |debouncer__parameterized0_2  |    56|
|25    |    \genblk1[1].debouncer_gen[0].deb_ins  |debouncer__parameterized0_3  |    56|
|26    |    \genblk1[1].debouncer_gen[1].deb_ins  |debouncer__parameterized0_4  |    56|
|27    |    \genblk1[1].debouncer_gen[2].deb_ins  |debouncer__parameterized0_5  |    56|
|28    |    \genblk1[1].debouncer_gen[3].deb_ins  |debouncer__parameterized0_6  |    56|
|29    |    \genblk1[2].debouncer_gen[0].deb_ins  |debouncer__parameterized0_7  |    56|
|30    |    \genblk1[2].debouncer_gen[1].deb_ins  |debouncer__parameterized0_8  |    56|
|31    |    \genblk1[2].debouncer_gen[2].deb_ins  |debouncer__parameterized0_9  |    56|
|32    |    \genblk1[2].debouncer_gen[3].deb_ins  |debouncer__parameterized0_10 |    56|
|33    |    \genblk1[3].debouncer_gen[0].deb_ins  |debouncer__parameterized0_11 |    56|
|34    |    \genblk1[3].debouncer_gen[1].deb_ins  |debouncer__parameterized0_12 |    56|
|35    |    \genblk1[3].debouncer_gen[2].deb_ins  |debouncer__parameterized0_13 |    56|
|36    |    \genblk1[3].debouncer_gen[3].deb_ins  |debouncer__parameterized0_14 |    56|
|37    |    k_div_ins                             |divider                      |    15|
+------+------------------------------------------+-----------------------------+------+
---------------------------------------------------------------------------------
Finished Writing Synthesis Report : Time (s): cpu = 00:00:24 ; elapsed = 00:00:27 . Memory (MB): peak = 924.137 ; gain = 592.102
---------------------------------------------------------------------------------
Synthesis finished with 0 errors, 0 critical warnings and 4 warnings.
Synthesis Optimization Runtime : Time (s): cpu = 00:00:19 ; elapsed = 00:00:23 . Memory (MB): peak = 924.137 ; gain = 229.719
Synthesis Optimization Complete : Time (s): cpu = 00:00:24 ; elapsed = 00:00:27 . Memory (MB): peak = 924.137 ; gain = 592.102
INFO: [Project 1-571] Translating synthesized netlist
INFO: [Netlist 29-17] Analyzing 198 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 924.137 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

INFO: [Common 17-83] Releasing license: Synthesis
92 Infos, 13 Warnings, 0 Critical Warnings and 0 Errors encountered.
synth_design completed successfully
synth_design: Time (s): cpu = 00:00:26 ; elapsed = 00:00:29 . Memory (MB): peak = 924.137 ; gain = 605.262
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 924.137 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'E:/YS/clock_2/clock_2.runs/synth_1/top_module.dcp' has been generated.
INFO: [runtcl-4] Executing : report_utilization -file top_module_utilization_synth.rpt -pb top_module_utilization_synth.pb
INFO: [Common 17-206] Exiting Vivado at Tue Jun  3 22:38:43 2025...
