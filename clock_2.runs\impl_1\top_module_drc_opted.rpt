Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
---------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Tue Jun  3 22:39:07 2025
| Host         : DESKTOP-5LO6MT6 running 64-bit major release  (build 9200)
| Command      : report_drc -file top_module_drc_opted.rpt -pb top_module_drc_opted.pb -rpx top_module_drc_opted.rpx
| Design       : top_module
| Device       : xc7a100tfgg484-1
| Speed File   : -1
| Design State : Synthesized
---------------------------------------------------------------------------------------------------------------------

Report DRC

Table of Contents
-----------------
1. REPORT SUMMARY
2. REPORT DETAILS

1. REPORT SUMMARY
-----------------
            Netlist: netlist
          Floorplan: design_1
      Design limits: <entire design considered>
           Ruledeck: default
             Max violations: <unlimited>
             Violations found: 2
+-------------+----------+-------------------------------------------------------------+------------+
| Rule        | Severity | Description                                                 | Violations |
+-------------+----------+-------------------------------------------------------------+------------+
| CFGBVS-1    | Warning  | Missing CFGBVS and CONFIG_VOLTAGE Design Properties         | 1          |
| PLHOLDVIO-2 | Warning  | Non-Optimal connections which could lead to hold violations | 1          |
+-------------+----------+-------------------------------------------------------------+------------+

2. REPORT DETAILS
-----------------
CFGBVS-1#1 Warning
Missing CFGBVS and CONFIG_VOLTAGE Design Properties  
Neither the CFGBVS nor CONFIG_VOLTAGE voltage property is set in the current_design.  Configuration bank voltage select (CFGBVS) must be set to VCCO or GND, and CONFIG_VOLTAGE must be set to the correct configuration voltage, in order to determine the I/O voltage support for the pins in bank 0.  It is suggested to specify these either using the 'Edit Device Properties' function in the GUI or directly in the XDC file using the following syntax:

 set_property CFGBVS value1 [current_design]
 #where value1 is either VCCO or GND

 set_property CONFIG_VOLTAGE value2 [current_design]
 #where value2 is the voltage provided to configuration bank 0

Refer to the device configuration user guide for more information.
Related violations: <none>

PLHOLDVIO-2#1 Warning
Non-Optimal connections which could lead to hold violations  
A LUT c_ins/timer/state[2]_i_2 is driving clock pin of 3 cells. This could lead to large hold time violations. Involved cells are:
c_ins/state_reg[0], c_ins/state_reg[1], c_ins/state_reg[2]
Related violations: <none>


