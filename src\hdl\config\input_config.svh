`ifndef _INPUT_CONFIG_H_
`define _INPUT_CONFIG_H_

// ========================================
// 键盘按键编码定义
// ========================================
`define KEY_0 4'h0           // 数字键'0'
`define KEY_1 4'h1           // 数字键'1'
`define KEY_2 4'h2           // 数字键'2'
`define KEY_3 4'h3           // 数字键'3'
`define KEY_4 4'h4           // 数字键'4'
`define KEY_5 4'h5           // 数字键'5'
`define KEY_6 4'h6           // 数字键'6'
`define KEY_7 4'h7           // 数字键'7'
`define KEY_8 4'h8           // 数字键'8'
`define KEY_9 4'h9           // 数字键'9'
`define KEY_A 4'ha           // 功能键'A'
`define KEY_B 4'hb           // 功能键'B'
`define KEY_C 4'hc           // 功能键'C'
`define KEY_D 4'hd           // 功能键'D' - 闹钟激活切换
`define KEY_E 4'he           // 功能键'E'
`define KEY_F 4'hf           // 功能键'F'

// ========================================
// 输入设备配置
// ========================================
`define BUTTON_COUNT 5       // 按键数量
`define KEYBOARD_ROWS 4      // 键盘行数
`define KEYBOARD_COLS 4      // 键盘列数
`define DEBOUNCE_MS 10       // 防抖时间（毫秒）

`endif
