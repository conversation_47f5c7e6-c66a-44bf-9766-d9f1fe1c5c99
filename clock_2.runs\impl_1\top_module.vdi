#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Tue Jun  3 22:38:50 2025
# Process ID: 35220
# Current directory: E:/YS/clock_2/clock_2.runs/impl_1
# Command line: vivado.exe -log top_module.vdi -applog -product Vivado -messageDb vivado.pb -mode batch -source top_module.tcl -notrace
# Log file: E:/YS/clock_2/clock_2.runs/impl_1/top_module.vdi
# Journal file: E:/YS/clock_2/clock_2.runs/impl_1\vivado.jou
#-----------------------------------------------------------
source top_module.tcl -notrace
Command: link_design -top top_module -part xc7a100tfgg484-1
Design is defaulting to srcset: sources_1
Design is defaulting to constrset: constrs_1
INFO: [Netlist 29-17] Analyzing 198 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2018.3
INFO: [Device 21-403] Loading part xc7a100tfgg484-1
INFO: [Project 1-570] Preparing netlist for logic optimization
Parsing XDC File [E:/YS/clock_2/src/xdc/constraints.xdc]
Finished Parsing XDC File [E:/YS/clock_2/src/xdc/constraints.xdc]
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 664.383 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

7 Infos, 0 Warnings, 0 Critical Warnings and 0 Errors encountered.
link_design completed successfully
link_design: Time (s): cpu = 00:00:04 ; elapsed = 00:00:05 . Memory (MB): peak = 668.559 ; gain = 343.797
Command: opt_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command opt_design

Starting DRC Task
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Project 1-461] DRC finished with 0 Errors
INFO: [Project 1-462] Please refer to the DRC report (report_drc) for more information.

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.376 . Memory (MB): peak = 675.773 ; gain = 7.215

Starting Cache Timing Information Task
INFO: [Timing 38-35] Done setting XDC timing constraints.
Ending Cache Timing Information Task | Checksum: f70db812

Time (s): cpu = 00:00:06 ; elapsed = 00:00:06 . Memory (MB): peak = 1237.953 ; gain = 562.180

Starting Logic Optimization Task

Phase 1 Retarget
INFO: [Opt 31-138] Pushed 1 inverter(s) to 33 load pin(s).
INFO: [Opt 31-49] Retargeted 0 cell(s).
Phase 1 Retarget | Checksum: 1f24f7fa1

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.193 . Memory (MB): peak = 1335.207 ; gain = 0.000
INFO: [Opt 31-389] Phase Retarget created 7 cells and removed 29 cells

Phase 2 Constant propagation
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Phase 2 Constant propagation | Checksum: 1f24f7fa1

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.216 . Memory (MB): peak = 1335.207 ; gain = 0.000
INFO: [Opt 31-389] Phase Constant propagation created 0 cells and removed 0 cells

Phase 3 Sweep
Phase 3 Sweep | Checksum: 19d34c81d

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.254 . Memory (MB): peak = 1335.207 ; gain = 0.000
INFO: [Opt 31-389] Phase Sweep created 0 cells and removed 0 cells

Phase 4 BUFG optimization
Phase 4 BUFG optimization | Checksum: 19d34c81d

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.284 . Memory (MB): peak = 1335.207 ; gain = 0.000
INFO: [Opt 31-662] Phase BUFG optimization created 0 cells of which 0 are BUFGs and removed 0 cells.

Phase 5 Shift Register Optimization
Phase 5 Shift Register Optimization | Checksum: ce49b848

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.421 . Memory (MB): peak = 1335.207 ; gain = 0.000
INFO: [Opt 31-389] Phase Shift Register Optimization created 0 cells and removed 0 cells

Phase 6 Post Processing Netlist
Phase 6 Post Processing Netlist | Checksum: ce49b848

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.432 . Memory (MB): peak = 1335.207 ; gain = 0.000
INFO: [Opt 31-389] Phase Post Processing Netlist created 0 cells and removed 0 cells
Opt_design Change Summary
=========================


-------------------------------------------------------------------------------------------------------------------------
|  Phase                        |  #Cells created  |  #Cells Removed  |  #Constrained objects preventing optimizations  |
-------------------------------------------------------------------------------------------------------------------------
|  Retarget                     |               7  |              29  |                                              0  |
|  Constant propagation         |               0  |               0  |                                              0  |
|  Sweep                        |               0  |               0  |                                              0  |
|  BUFG optimization            |               0  |               0  |                                              0  |
|  Shift Register Optimization  |               0  |               0  |                                              0  |
|  Post Processing Netlist      |               0  |               0  |                                              0  |
-------------------------------------------------------------------------------------------------------------------------



Starting Connectivity Check Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.003 . Memory (MB): peak = 1335.207 ; gain = 0.000
Ending Logic Optimization Task | Checksum: ce49b848

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.441 . Memory (MB): peak = 1335.207 ; gain = 0.000

Starting Power Optimization Task
INFO: [Pwropt 34-132] Skipping clock gating for clocks with a period < 2.00 ns.
Ending Power Optimization Task | Checksum: ce49b848

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.009 . Memory (MB): peak = 1335.207 ; gain = 0.000

Starting Final Cleanup Task
Ending Final Cleanup Task | Checksum: ce49b848

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1335.207 ; gain = 0.000

Starting Netlist Obfuscation Task
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1335.207 ; gain = 0.000
Ending Netlist Obfuscation Task | Checksum: ce49b848

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1335.207 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
23 Infos, 0 Warnings, 0 Critical Warnings and 0 Errors encountered.
opt_design completed successfully
opt_design: Time (s): cpu = 00:00:08 ; elapsed = 00:00:08 . Memory (MB): peak = 1335.207 ; gain = 666.648
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1335.207 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.020 . Memory (MB): peak = 1335.207 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1335.207 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'E:/YS/clock_2/clock_2.runs/impl_1/top_module_opt.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file top_module_drc_opted.rpt -pb top_module_drc_opted.pb -rpx top_module_drc_opted.rpx
Command: report_drc -file top_module_drc_opted.rpt -pb top_module_drc_opted.pb -rpx top_module_drc_opted.rpx
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'D:/Xilinx/Vivado/2018.3/data/ip'.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file E:/YS/clock_2/clock_2.runs/impl_1/top_module_drc_opted.rpt.
report_drc completed successfully
Command: place_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.
Running DRC as a precondition to command place_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.

Starting Placer Task
INFO: [Place 30-611] Multithreading enabled for place_design using a maximum of 2 CPUs

Phase 1 Placer Initialization

Phase 1.1 Placer Initialization Netlist Sorting
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1335.207 ; gain = 0.000
Phase 1.1 Placer Initialization Netlist Sorting | Checksum: b71f2207

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.018 . Memory (MB): peak = 1335.207 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1335.207 ; gain = 0.000

Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device
INFO: [Timing 38-35] Done setting XDC timing constraints.
WARNING: [Place 30-568] A LUT 'c_ins/timer/state[2]_i_2' is driving clock pin of 3 registers. This could lead to large hold time violations. First few involved registers are:
	c_ins/state_reg[0] {FDCE}
	c_ins/state_reg[1] {FDCE}
	c_ins/state_reg[2] {FDCE}
Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device | Checksum: 8d1579d5

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.685 . Memory (MB): peak = 1341.391 ; gain = 6.184

Phase 1.3 Build Placer Netlist Model
Phase 1.3 Build Placer Netlist Model | Checksum: 120addc00

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 1.4 Constrain Clocks/Macros
Phase 1.4 Constrain Clocks/Macros | Checksum: 120addc00

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1357.852 ; gain = 22.645
Phase 1 Placer Initialization | Checksum: 120addc00

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 2 Global Placement

Phase 2.1 Floorplanning
Phase 2.1 Floorplanning | Checksum: 1678f673b

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 2.2 Physical Synthesis In Placer
INFO: [Physopt 32-65] No nets found for high-fanout optimization.
INFO: [Physopt 32-232] Optimized 0 net. Created 0 new instance.
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-456] No candidate cells for DSP register optimization found in the design.
INFO: [Physopt 32-775] End 2 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-677] No candidate cells for Shift Register optimization found in the design
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-526] No candidate cells for BRAM register optimization found in the design
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-949] No candidate nets found for HD net replication
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1357.852 ; gain = 0.000

Summary of Physical Synthesis Optimizations
============================================


----------------------------------------------------------------------------------------------------------------------------------------
|  Optimization                  |  Added Cells  |  Removed Cells  |  Optimized Cells/Nets  |  Dont Touch  |  Iterations  |  Elapsed   |
----------------------------------------------------------------------------------------------------------------------------------------
|  Very High Fanout              |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  DSP Register                  |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Shift Register                |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  BRAM Register                 |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  HD Interface Net Replication  |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Total                         |            0  |              0  |                     0  |           0  |           5  |  00:00:00  |
----------------------------------------------------------------------------------------------------------------------------------------


Phase 2.2 Physical Synthesis In Placer | Checksum: 12cb2cf5b

Time (s): cpu = 00:00:03 ; elapsed = 00:00:02 . Memory (MB): peak = 1357.852 ; gain = 22.645
Phase 2 Global Placement | Checksum: 9adcc722

Time (s): cpu = 00:00:03 ; elapsed = 00:00:02 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 3 Detail Placement

Phase 3.1 Commit Multi Column Macros
Phase 3.1 Commit Multi Column Macros | Checksum: 9adcc722

Time (s): cpu = 00:00:03 ; elapsed = 00:00:02 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 3.2 Commit Most Macros & LUTRAMs
Phase 3.2 Commit Most Macros & LUTRAMs | Checksum: 14cddfdfd

Time (s): cpu = 00:00:04 ; elapsed = 00:00:02 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 3.3 Area Swap Optimization
Phase 3.3 Area Swap Optimization | Checksum: 15223e8fb

Time (s): cpu = 00:00:04 ; elapsed = 00:00:02 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 3.4 Pipeline Register Optimization
Phase 3.4 Pipeline Register Optimization | Checksum: 1da5fdaab

Time (s): cpu = 00:00:04 ; elapsed = 00:00:02 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 3.5 Fast Optimization
Phase 3.5 Fast Optimization | Checksum: 184c3c52e

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 3.6 Small Shape Detail Placement
Phase 3.6 Small Shape Detail Placement | Checksum: 11efd7092

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 3.7 Re-assign LUT pins
Phase 3.7 Re-assign LUT pins | Checksum: 17f3b805e

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 3.8 Pipeline Register Optimization
Phase 3.8 Pipeline Register Optimization | Checksum: 106f76eae

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 3.9 Fast Optimization
Phase 3.9 Fast Optimization | Checksum: 103fd7ef0

Time (s): cpu = 00:00:05 ; elapsed = 00:00:03 . Memory (MB): peak = 1357.852 ; gain = 22.645
Phase 3 Detail Placement | Checksum: 103fd7ef0

Time (s): cpu = 00:00:05 ; elapsed = 00:00:03 . Memory (MB): peak = 1357.852 ; gain = 22.645

Phase 4 Post Placement Optimization and Clean-Up

Phase 4.1 Post Commit Optimization
INFO: [Timing 38-35] Done setting XDC timing constraints.

Phase 4.1.1 Post Placement Optimization
Post Placement Optimization Initialization | Checksum: 202304b03

Phase ******* BUFG Insertion
INFO: [Place 46-46] BUFG insertion identified 0 candidate nets, 0 success, 0 bufg driver replicated, 0 skipped for placement/routing, 0 skipped for timing, 0 skipped for netlist change reason
Phase ******* BUFG Insertion | Checksum: 202304b03

Time (s): cpu = 00:00:05 ; elapsed = 00:00:04 . Memory (MB): peak = 1362.988 ; gain = 27.781
INFO: [Place 30-746] Post Placement Timing Summary WNS=-9.417. For the most accurate timing information please run report_timing.
Phase 4.1.1 Post Placement Optimization | Checksum: 1ef4643d1

Time (s): cpu = 00:00:06 ; elapsed = 00:00:05 . Memory (MB): peak = 1362.988 ; gain = 27.781
Phase 4.1 Post Commit Optimization | Checksum: 1ef4643d1

Time (s): cpu = 00:00:06 ; elapsed = 00:00:05 . Memory (MB): peak = 1362.988 ; gain = 27.781

Phase 4.2 Post Placement Cleanup
Phase 4.2 Post Placement Cleanup | Checksum: 1ef4643d1

Time (s): cpu = 00:00:06 ; elapsed = 00:00:05 . Memory (MB): peak = 1362.996 ; gain = 27.789

Phase 4.3 Placer Reporting
Phase 4.3 Placer Reporting | Checksum: 1ef4643d1

Time (s): cpu = 00:00:06 ; elapsed = 00:00:05 . Memory (MB): peak = 1362.996 ; gain = 27.789

Phase 4.4 Final Placement Cleanup
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1362.996 ; gain = 0.000
Phase 4.4 Final Placement Cleanup | Checksum: 1dc3e61eb

Time (s): cpu = 00:00:06 ; elapsed = 00:00:05 . Memory (MB): peak = 1362.996 ; gain = 27.789
Phase 4 Post Placement Optimization and Clean-Up | Checksum: 1dc3e61eb

Time (s): cpu = 00:00:06 ; elapsed = 00:00:05 . Memory (MB): peak = 1362.996 ; gain = 27.789
Ending Placer Task | Checksum: 15d06d76f

Time (s): cpu = 00:00:06 ; elapsed = 00:00:05 . Memory (MB): peak = 1362.996 ; gain = 27.789
INFO: [Common 17-83] Releasing license: Implementation
55 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
place_design completed successfully
place_design: Time (s): cpu = 00:00:07 ; elapsed = 00:00:06 . Memory (MB): peak = 1362.996 ; gain = 27.789
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1362.996 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1370.582 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.124 . Memory (MB): peak = 1370.582 ; gain = 7.586
INFO: [Common 17-1381] The checkpoint 'E:/YS/clock_2/clock_2.runs/impl_1/top_module_placed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_io -file top_module_io_placed.rpt
report_io: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.044 . Memory (MB): peak = 1370.582 ; gain = 0.000
INFO: [runtcl-4] Executing : report_utilization -file top_module_utilization_placed.rpt -pb top_module_utilization_placed.pb
INFO: [runtcl-4] Executing : report_control_sets -verbose -file top_module_control_sets_placed.rpt
report_control_sets: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.007 . Memory (MB): peak = 1370.582 ; gain = 0.000
Command: route_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command route_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.


Starting Routing Task
INFO: [Route 35-254] Multithreading enabled for route_design using a maximum of 2 CPUs
Checksum: PlaceDB: bcbde7c9 ConstDB: 0 ShapeSum: a048efa6 RouteDB: 0

Phase 1 Build RT Design
Phase 1 Build RT Design | Checksum: 6bc68bc8

Time (s): cpu = 00:00:19 ; elapsed = 00:00:17 . Memory (MB): peak = 1521.539 ; gain = 150.957
Post Restoration Checksum: NetGraph: 62b67dc NumContArr: 659b23ec Constraints: 0 Timing: 0

Phase 2 Router Initialization

Phase 2.1 Create Timer
Phase 2.1 Create Timer | Checksum: 6bc68bc8

Time (s): cpu = 00:00:19 ; elapsed = 00:00:17 . Memory (MB): peak = 1553.871 ; gain = 183.289

Phase 2.2 Fix Topology Constraints
Phase 2.2 Fix Topology Constraints | Checksum: 6bc68bc8

Time (s): cpu = 00:00:19 ; elapsed = 00:00:17 . Memory (MB): peak = 1560.004 ; gain = 189.422

Phase 2.3 Pre Route Cleanup
Phase 2.3 Pre Route Cleanup | Checksum: 6bc68bc8

Time (s): cpu = 00:00:19 ; elapsed = 00:00:17 . Memory (MB): peak = 1560.004 ; gain = 189.422
 Number of Nodes with overlaps = 0

Phase 2.4 Update Timing
Phase 2.4 Update Timing | Checksum: 1abe5896b

Time (s): cpu = 00:00:19 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-9.409 | TNS=-9.409 | WHS=-1.247 | THS=-8.069 |

Phase 2 Router Initialization | Checksum: fd21c0a9

Time (s): cpu = 00:00:19 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707

Phase 3 Initial Routing
Phase 3 Initial Routing | Checksum: 22c95afdf

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707

Phase 4 Rip-up And Reroute

Phase 4.1 Global Iteration 0
 Number of Nodes with overlaps = 179
 Number of Nodes with overlaps = 1
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-9.116 | TNS=-9.116 | WHS=N/A    | THS=N/A    |

Phase 4.1 Global Iteration 0 | Checksum: 1a2ea7eb1

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707

Phase 4.2 Global Iteration 1
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-9.116 | TNS=-9.116 | WHS=N/A    | THS=N/A    |

Phase 4.2 Global Iteration 1 | Checksum: 192d4fae2

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707
Phase 4 Rip-up And Reroute | Checksum: 192d4fae2

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707

Phase 5 Delay and Skew Optimization

Phase 5.1 Delay CleanUp

Phase 5.1.1 Update Timing
Phase 5.1.1 Update Timing | Checksum: 11bbb57c5

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-9.116 | TNS=-9.116 | WHS=N/A    | THS=N/A    |

 Number of Nodes with overlaps = 0
Phase 5.1 Delay CleanUp | Checksum: 117cb76cb

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707

Phase 5.2 Clock Skew Optimization
Phase 5.2 Clock Skew Optimization | Checksum: 117cb76cb

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707
Phase 5 Delay and Skew Optimization | Checksum: 117cb76cb

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707

Phase 6 Post Hold Fix

Phase 6.1 Hold Fix Iter

Phase 6.1.1 Update Timing
Phase 6.1.1 Update Timing | Checksum: 1d23317aa

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-9.116 | TNS=-9.116 | WHS=0.021  | THS=0.000  |

Phase 6.1 Hold Fix Iter | Checksum: 12876f837

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707
Phase 6 Post Hold Fix | Checksum: 12876f837

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1584.289 ; gain = 213.707

Phase 7 Route finalize

Router Utilization Summary
  Global Vertical Routing Utilization    = 0.237194 %
  Global Horizontal Routing Utilization  = 0.259449 %
  Routable Net Status*
  *Does not include unroutable nets such as driverless and loadless.
  Run report_route_status for detailed report.
  Number of Failed Nets               = 0
  Number of Unrouted Nets             = 0
  Number of Partially Routed Nets     = 0
  Number of Node Overlaps             = 0

Congestion Report
North Dir 1x1 Area, Max Cong = 30.6306%, No Congested Regions.
South Dir 1x1 Area, Max Cong = 29.7297%, No Congested Regions.
East Dir 1x1 Area, Max Cong = 35.2941%, No Congested Regions.
West Dir 1x1 Area, Max Cong = 54.4118%, No Congested Regions.

------------------------------
Reporting congestion hotspots
------------------------------
Direction: North
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: South
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: East
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: West
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0

Phase 7 Route finalize | Checksum: 110d8685d

Time (s): cpu = 00:00:20 ; elapsed = 00:00:19 . Memory (MB): peak = 1584.289 ; gain = 213.707

Phase 8 Verifying routed nets

 Verification completed successfully
Phase 8 Verifying routed nets | Checksum: 110d8685d

Time (s): cpu = 00:00:20 ; elapsed = 00:00:19 . Memory (MB): peak = 1584.289 ; gain = 213.707

Phase 9 Depositing Routes
Phase 9 Depositing Routes | Checksum: 12ea1a7ae

Time (s): cpu = 00:00:21 ; elapsed = 00:00:19 . Memory (MB): peak = 1584.289 ; gain = 213.707

Phase 10 Post Router Timing
INFO: [Route 35-57] Estimated Timing Summary | WNS=-9.116 | TNS=-9.116 | WHS=0.021  | THS=0.000  |

WARNING: [Route 35-328] Router estimated timing not met.
Resolution: For a complete and accurate timing signoff, report_timing_summary must be run after route_design. Alternatively, route_design can be run with the -timing_summary option to enable a complete timing signoff at the end of route_design.
Phase 10 Post Router Timing | Checksum: 12ea1a7ae

Time (s): cpu = 00:00:21 ; elapsed = 00:00:19 . Memory (MB): peak = 1584.289 ; gain = 213.707
INFO: [Route 35-16] Router Completed Successfully

Time (s): cpu = 00:00:21 ; elapsed = 00:00:19 . Memory (MB): peak = 1584.289 ; gain = 213.707

Routing Is Done.
INFO: [Common 17-83] Releasing license: Implementation
73 Infos, 2 Warnings, 0 Critical Warnings and 0 Errors encountered.
route_design completed successfully
route_design: Time (s): cpu = 00:00:22 ; elapsed = 00:00:19 . Memory (MB): peak = 1584.289 ; gain = 213.707
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1584.289 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1584.289 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.153 . Memory (MB): peak = 1584.289 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'E:/YS/clock_2/clock_2.runs/impl_1/top_module_routed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file top_module_drc_routed.rpt -pb top_module_drc_routed.pb -rpx top_module_drc_routed.rpx
Command: report_drc -file top_module_drc_routed.rpt -pb top_module_drc_routed.pb -rpx top_module_drc_routed.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file E:/YS/clock_2/clock_2.runs/impl_1/top_module_drc_routed.rpt.
report_drc completed successfully
INFO: [runtcl-4] Executing : report_methodology -file top_module_methodology_drc_routed.rpt -pb top_module_methodology_drc_routed.pb -rpx top_module_methodology_drc_routed.rpx
Command: report_methodology -file top_module_methodology_drc_routed.rpt -pb top_module_methodology_drc_routed.pb -rpx top_module_methodology_drc_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [DRC 23-133] Running Methodology with 2 threads
INFO: [Coretcl 2-1520] The results of Report Methodology are in file E:/YS/clock_2/clock_2.runs/impl_1/top_module_methodology_drc_routed.rpt.
report_methodology completed successfully
INFO: [runtcl-4] Executing : report_power -file top_module_power_routed.rpt -pb top_module_power_summary_routed.pb -rpx top_module_power_routed.rpx
Command: report_power -file top_module_power_routed.rpt -pb top_module_power_summary_routed.pb -rpx top_module_power_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
Running Vector-less Activity Propagation...

Finished Running Vector-less Activity Propagation
85 Infos, 2 Warnings, 0 Critical Warnings and 0 Errors encountered.
report_power completed successfully
INFO: [runtcl-4] Executing : report_route_status -file top_module_route_status.rpt -pb top_module_route_status.pb
INFO: [runtcl-4] Executing : report_timing_summary -max_paths 10 -file top_module_timing_summary_routed.rpt -pb top_module_timing_summary_routed.pb -rpx top_module_timing_summary_routed.rpx -warn_on_violation 
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
CRITICAL WARNING: [Timing 38-282] The design failed to meet the timing requirements. Please see the timing summary report for details on the timing violations.
INFO: [runtcl-4] Executing : report_incremental_reuse -file top_module_incremental_reuse_routed.rpt
INFO: [Vivado_Tcl 4-1062] Incremental flow is disabled. No incremental reuse Info to report.
INFO: [runtcl-4] Executing : report_clock_utilization -file top_module_clock_utilization_routed.rpt
INFO: [runtcl-4] Executing : report_bus_skew -warn_on_violation -file top_module_bus_skew_routed.rpt -pb top_module_bus_skew_routed.pb -rpx top_module_bus_skew_routed.rpx
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
Command: write_bitstream -force top_module.bit
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command write_bitstream
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
WARNING: [DRC CFGBVS-1] Missing CFGBVS and CONFIG_VOLTAGE Design Properties: Neither the CFGBVS nor CONFIG_VOLTAGE voltage property is set in the current_design.  Configuration bank voltage select (CFGBVS) must be set to VCCO or GND, and CONFIG_VOLTAGE must be set to the correct configuration voltage, in order to determine the I/O voltage support for the pins in bank 0.  It is suggested to specify these either using the 'Edit Device Properties' function in the GUI or directly in the XDC file using the following syntax:

 set_property CFGBVS value1 [current_design]
 #where value1 is either VCCO or GND

 set_property CONFIG_VOLTAGE value2 [current_design]
 #where value2 is the voltage provided to configuration bank 0

Refer to the device configuration user guide for more information.
WARNING: [DRC PDRC-153] Gated clock check: Net c_ins/timer/T[1]_0 is a gated clock net sourced by a combinational pin c_ins/timer/state[2]_i_2/O, cell c_ins/timer/state[2]_i_2. This is not good design practice and will likely impact performance. For SLICE registers, for example, use the CE pin to control the loading of data.
WARNING: [DRC PLHOLDVIO-2] Non-Optimal connections which could lead to hold violations: A LUT c_ins/timer/state[2]_i_2 is driving clock pin of 3 cells. This could lead to large hold time violations. Involved cells are:
c_ins/state_reg[0], c_ins/state_reg[1], and c_ins/state_reg[2]
INFO: [Vivado 12-3199] DRC finished with 0 Errors, 3 Warnings
INFO: [Vivado 12-3200] Please refer to the DRC report (report_drc) for more information.
INFO: [Designutils 20-2272] Running write_bitstream with 2 threads.
Loading data files...
Loading site data...
Loading route data...
Processing options...
Creating bitmap...
Creating bitstream...
Writing bitstream ./top_module.bit...
INFO: [Vivado 12-1842] Bitgen Completed Successfully.
INFO: [Project 1-120] WebTalk data collection is mandatory when using a WebPACK part without a full Vivado license. To see the specific WebTalk data collected for your design, open the usage_statistics_webtalk.html or usage_statistics_webtalk.xml file in the implementation directory.
INFO: [Common 17-83] Releasing license: Implementation
104 Infos, 5 Warnings, 1 Critical Warnings and 0 Errors encountered.
write_bitstream completed successfully
write_bitstream: Time (s): cpu = 00:00:10 ; elapsed = 00:00:10 . Memory (MB): peak = 2033.621 ; gain = 431.672
INFO: [Common 17-206] Exiting Vivado at Tue Jun  3 22:39:45 2025...
