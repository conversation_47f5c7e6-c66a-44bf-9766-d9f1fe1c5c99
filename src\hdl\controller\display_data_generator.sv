`include "config.svh"

module display_data_generator (
    input logic clock,
    input logic reset_n,
    input state_t state,
    input logic [2:0] h_ptr,
    input logic [1:0] v_ptr,
    input logic [23:0] time_data,
    input logic [95:0] alarm_data,
    input logic [3:0] is_activated,
    input logic [23:0] timer_data,  // 添加计时器数据输入
    input logic timer_running,  // 添加计时器运行状态输入
    input logic [23:0] countdown_data,  // 添加倒计时数据输入
    input logic countdown_running,  // 添加倒计时运行状态输入
    output logic [7:0][5:0] display_code
);
    logic [5:0][3:0] __time_data;
    assign __time_data = time_data;
    logic [3:0][5:0][3:0] __alarm_data;
    assign __alarm_data = alarm_data;
    logic [5:0][3:0] __timer_data;  // 添加计时器数据重新解释
    assign __timer_data = timer_data;
    logic [5:0][3:0] __countdown_data;  // 添加倒计时数据重新解释
    assign __countdown_data = countdown_data;

    localparam divisor = `frequency / 4 / `frequency_fliping;

    logic flip;
    divider #(divisor) flip_ins (
        .clock(clock),
        .reset_n(reset_n),
        .clock_out(flip)
    );

    always @(posedge clock or negedge reset_n) begin
        if (~reset_n) begin
            display_code <= 0;
        end else begin
            case (state)
                normal_state: begin
                    display_code[7] = |is_activated ? `CHAR_A : `CHAR_BLANK;
                    display_code[6] = `CHAR_BLANK;
                    for (int i = 0; i < `TIME_DIGITS; i++) begin
                        display_code[i] = {2'b00, __time_data[i]};
                    end
                end
                time_setting_state: begin
                    display_code[7] = `CHAR_BLANK;
                    display_code[6] = `CHAR_BLANK;
                    for (int i = 0; i < `TIME_DIGITS; i++) begin
                        display_code[i] = (flip && i == h_ptr) ? `CHAR_BLANK : {2'b00, __time_data[i]};
                    end
                end
                alarm_setting_state: begin
                    display_code[7] = is_activated[v_ptr] ? `CHAR_A : `CHAR_BLANK;
                    display_code[6] = v_ptr;
                    for (int i = 0; i < `TIME_DIGITS; i++) begin
                        display_code[i] = (flip && i == h_ptr) ? `CHAR_BLANK : {2'b00, __alarm_data[v_ptr][i]};
                    end
                end
                timer_setting_state: begin
                    // 显示计时器状态：最左侧显示运行状态，第二位显示"t"表示计时器模式
                    display_code[7] = timer_running ? (flip ? `CHAR_F : `CHAR_E) : `CHAR_BLANK;
                    display_code[6] = `CHAR_T;  // 显示"t"表示计时器模式
                    for (int i = 0; i < `TIME_DIGITS; i++) begin
                        display_code[i] = {2'b00, __timer_data[i]};
                    end
                end
                countdown_setting_state: begin
                    // 显示倒计时状态：最左侧显示运行状态，第二位显示"C"表示倒计时模式
                    display_code[7] = countdown_running ? (flip ? `CHAR_F : `CHAR_E) : `CHAR_BLANK;
                    display_code[6] = `CHAR_C;  // 显示"C"表示倒计时模式
                    for (int i = 0; i < `TIME_DIGITS; i++) begin
                        // 在设置状态下显示闪烁光标，在运行状态下不闪烁
                        display_code[i] = (flip && i == h_ptr && ~countdown_running) ? `CHAR_BLANK : {2'b00, __countdown_data[i]};
                    end
                end

                default: display_code = {8{`CHAR_BLANK}};
            endcase
        end
    end
endmodule
