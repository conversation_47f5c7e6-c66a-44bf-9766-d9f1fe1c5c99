`include "config.svh"

module data_modifier (
    input logic clock,
    input logic reset_n,
    input state_t state,
    input logic [2:0] h_ptr,
    input logic [1:0] v_ptr,
    input logic [3:0][3:0] keyboard_down,
    input logic [4:0] button_down,  // 添加按键输入
    output logic [5:0][3:0] time_data,
    output logic [3:0][5:0][3:0] alarm_data,
    output logic [3:0] is_activated,
    output logic [5:0][3:0] timer_data,  // 添加计时器数据输出
    output logic timer_running,  // 添加计时器运行状态输出
    output logic [5:0][3:0] countdown_data,  // 添加倒计时数据输出
    output logic countdown_running,  // 添加倒计时运行状态输出
    output logic countdown_finished,  // 添加倒计时完成信号
    output logic keyboard_warning,
    output logic zero_ms
);

    logic [5:0] keyboard_down_count;
    always @(*) begin
        keyboard_down_count = 0;
        for (int i = 0; i < 4; i++)
            for (int j = 0; j < 4; j++)
                keyboard_down_count = keyboard_down_count + keyboard_down[i][j];
    end

    logic [15:0] reinterpreted_keyboard;

    assign reinterpreted_keyboard['h0] = keyboard_down[3][1];
    //assign reinterpreted_keyboard['h1] = keyboard_down[0][0];
    assign reinterpreted_keyboard['h2] = keyboard_down[0][1];
    assign reinterpreted_keyboard['h3] = keyboard_down[0][2];
    //assign reinterpreted_keyboard['h4] = keyboard_down[1][0];
    assign reinterpreted_keyboard['h5] = keyboard_down[1][1];
    assign reinterpreted_keyboard['h6] = keyboard_down[1][2];
    //assign reinterpreted_keyboard['h7] = keyboard_down[2][0];
    assign reinterpreted_keyboard['h8] = keyboard_down[2][1];
    assign reinterpreted_keyboard['h9] = keyboard_down[2][2];
    assign reinterpreted_keyboard['h1] = keyboard_down[0][3];  // a 替换为 1
    assign reinterpreted_keyboard['h4] = keyboard_down[1][3];  // b 替换为 4 
    assign reinterpreted_keyboard['h7] = keyboard_down[2][3];  // c 替换为 7
    assign reinterpreted_keyboard['hd] = keyboard_down[3][3];
    assign reinterpreted_keyboard['he] = keyboard_down[3][2];
    assign reinterpreted_keyboard['hf] = keyboard_down[3][0];

    logic [3:0] keyboard_down_number;
    always @(*) begin
        keyboard_down_number = 0;
        for (int i = 0; i < 16; i++)
            if (reinterpreted_keyboard[i])
                keyboard_down_number = keyboard_down_number | $unsigned(i);
    end

    // 通用时间验证函数
    function automatic logic is_valid_time_input(
        input logic [2:0] position,
        input logic [3:0] value,
        input logic [5:0][3:0] current_time
    );
        case (position)
            0: return (value < 10);                    // 秒个位：0-9
            1: return (value < 6);                     // 秒十位：0-5
            2: return (value < 10);                    // 分个位：0-9
            3: return (value < 6);                     // 分十位：0-5
            4: return (current_time[5] < 2 && value < 10) ||
                      (current_time[5] == 2 && value < 4); // 时个位：0-9或0-3(当十位为2时)
            5: return (value < 2) ||
                      (value == 2 && current_time[4] < 4); // 时十位：0-1或2(当个位<4时)
            default: return 1'b0;
        endcase
    endfunction

    // 通用时间递增函数
    function automatic logic [5:0][3:0] increment_time(
        input logic [5:0][3:0] current_time
    );
        logic [5:0][3:0] result;
        result = current_time;

        if (result[0] == 9) begin
            result[0] = 0;
            if (result[1] == 5) begin
                result[1] = 0;
                if (result[2] == 9) begin
                    result[2] = 0;
                    if (result[3] == 5) begin
                        result[3] = 0;
                        if (result[5] == 2 && result[4] == 3) begin
                            result[4] = 0;
                            result[5] = 0;
                        end else if (result[4] == 9) begin
                            result[4] = 0;
                            result[5] = result[5] + 1;
                        end else begin
                            result[4] = result[4] + 1;
                        end
                    end else begin
                        result[3] = result[3] + 1;
                    end
                end else begin
                    result[2] = result[2] + 1;
                end
            end else begin
                result[1] = result[1] + 1;
            end
        end else begin
            result[0] = result[0] + 1;
        end

        return result;
    endfunction

    // 通用时间递减函数
    function automatic logic [5:0][3:0] decrement_time(
        input logic [5:0][3:0] current_time
    );
        logic [5:0][3:0] result;
        result = current_time;

        if (result == 0) begin
            return result;  // 已经是0，不能再减
        end

        if (result[0] == 0) begin
            result[0] = 9;
            if (result[1] == 0) begin
                result[1] = 5;
                if (result[2] == 0) begin
                    result[2] = 9;
                    if (result[3] == 0) begin
                        result[3] = 5;
                        if (result[4] == 0) begin
                            if (result[5] == 0) begin
                                result = 0;  // 倒计时结束
                            end else begin
                                result[4] = 9;
                                result[5] = result[5] - 1;
                            end
                        end else begin
                            result[4] = result[4] - 1;
                        end
                    end else begin
                        result[3] = result[3] - 1;
                    end
                end else begin
                    result[2] = result[2] - 1;
                end
            end else begin
                result[1] = result[1] - 1;
            end
        end else begin
            result[0] = result[0] - 1;
        end

        return result;
    endfunction

    // T[2]是每4个周期中的一个使能信号，有效频率是25MHz
    // 要产生1秒的延时，需要25,000,000个T[2]使能周期
    localparam ms_count = `SYSTEM_FREQUENCY / 4;  // 25,000,000个周期 = 1秒
    logic [$clog2(ms_count)-1:0] ms;

    // 添加启动标志，用于在启动时重置毫秒计数器
    logic timer_just_started;
    logic countdown_just_started;



    always @(posedge clock or negedge reset_n) begin
        if (~reset_n) begin
            ms <= 0;
            time_data <= 0;
            for (int i = 0; i < `ALARM_COUNT; i++) begin
                alarm_data[i] <= 0;
                is_activated[i] <= 0;
            end
            timer_data <= 0;  // 初始化计时器数据
            timer_running <= 0;  // 初始化计时器运行状态
            countdown_data <= 0;  // 初始化倒计时数据
            countdown_running <= 0;  // 初始化倒计时运行状态
            countdown_finished <= 0;  // 初始化倒计时完成信号
            keyboard_warning <= 0;
            timer_just_started <= 0;  // 初始化计时器启动标志
            countdown_just_started <= 0;  // 初始化倒计时启动标志
        end else begin
            keyboard_warning <= 0;
            if (state == time_setting_state) begin
                if (keyboard_down_count == 1) begin
                    if (is_valid_time_input(h_ptr, keyboard_down_number, time_data)) begin
                        time_data[h_ptr] <= keyboard_down_number;
                        if (h_ptr == 0) ms <= 0;  // 重置毫秒计数器
                    end else begin
                        keyboard_warning <= 1;
                    end
                end else if (keyboard_down_count) begin
                    keyboard_warning <= 1;
                end
            end

            if (state == alarm_setting_state) begin
                if (keyboard_down_count == 1) begin
                    if (keyboard_down_number < 10) begin
                        if (is_valid_time_input(h_ptr, keyboard_down_number, alarm_data[v_ptr])) begin
                            alarm_data[v_ptr][h_ptr] <= keyboard_down_number;
                        end else begin
                            keyboard_warning <= 1;
                        end
                    end else if (keyboard_down_number == `KEY_D) begin  // D键切换激活状态
                        is_activated[v_ptr] <= ~is_activated[v_ptr];
                    end else begin
                        keyboard_warning <= 1;  // 其他非数字键发出警告音
                    end
                end else if (keyboard_down_count) begin
                    keyboard_warning <= 1;
                end
            end

            // 计时器模式处理
            if (state == timer_setting_state) begin
                // S1按键：开始/停止计时器
                if (button_down[0]) begin
                    timer_running <= ~timer_running;
                    // 启动计时器时设置启动标志
                    if (~timer_running) begin
                        timer_just_started <= 1;
                    end
                end
                // S2按键：清空计时器
                if (button_down[1]) begin
                    timer_data <= 0;
                    timer_running <= 0;
                end
                // 其他按键发出警告音（S4按键用于模式切换，不触发警告音）
                if (button_down[2] | button_down[4] | keyboard_down_count) begin
                    keyboard_warning <= 1;
                end
            end

            // 倒计时模式处理
            if (state == countdown_setting_state) begin
                // S1按键：光标右移
                if (button_down[0]) begin
                    // 光标移动逻辑与时间设置模式相同
                end
                // S2按键：光标左移
                if (button_down[1]) begin
                    // 光标移动逻辑与时间设置模式相同
                end
                // S3按键：开始/停止倒计时
                if (button_down[2]) begin
                    countdown_running <= ~countdown_running;
                    // 启动倒计时时设置启动标志
                    if (~countdown_running) begin
                        countdown_just_started <= 1;
                    end
                end
                // S5按键：清空倒计时
                if (button_down[4]) begin
                    countdown_data <= 0;
                    countdown_running <= 0;
                    countdown_finished <= 0;
                end
                // 数字键输入处理（使用通用验证函数）
                if (keyboard_down_count == 1) begin
                    if (is_valid_time_input(h_ptr, keyboard_down_number, countdown_data)) begin
                        countdown_data[h_ptr] <= keyboard_down_number;
                    end else begin
                        keyboard_warning <= 1;
                    end
                end else if (keyboard_down_count) begin
                    keyboard_warning <= 1;
                end
            end

            // 处理启动标志，重置毫秒计数器
            if (timer_just_started) begin
                ms <= 0;
                timer_just_started <= 0;
            end else if (countdown_just_started) begin
                ms <= 0;
                countdown_just_started <= 0;
            end else if (ms == ms_count - 1) begin
                ms <= 0;
                // 使用通用函数递增系统时间
                time_data <= increment_time(time_data);

                // 计时器计数逻辑（当计时器运行时）
                if (timer_running) begin
                    if (timer_data[5] == 2 && timer_data[4] == 3 &&
                        timer_data[3] == 5 && timer_data[2] == 9 &&
                        timer_data[1] == 5 && timer_data[0] == 9) begin
                        // 计时器达到最大值23:59:59，停止计时
                        timer_running <= 0;
                    end else begin
                        timer_data <= increment_time(timer_data);
                    end
                end

                // 倒计时递减逻辑（当倒计时运行时）
                if (countdown_running) begin
                    logic [5:0][3:0] new_countdown_data;
                    new_countdown_data = decrement_time(countdown_data);

                    if (new_countdown_data == 0) begin
                        // 倒计时结束
                        countdown_data <= 0;
                        countdown_running <= 0;
                        countdown_finished <= 1;
                    end else begin
                        countdown_data <= new_countdown_data;
                    end
                end else begin
                    // 清除倒计时完成信号（当不在运行状态时）
                    countdown_finished <= 0;
                end
            end else
                ms <= ms + 1;
        end
    end

    assign zero_ms = ~ (| ms);

endmodule
