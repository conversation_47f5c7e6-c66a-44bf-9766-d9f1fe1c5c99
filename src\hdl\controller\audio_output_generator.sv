`include "config.svh"

module audio_output_generator (
    input logic clock,
    input logic reset_n,
    input logic no_response,
    input logic alarm,
    input logic oclock,
    input logic warning,
    input logic countdown_finished,  // 添加倒计时完成信号
    output logic [31:0] frequency_select
);

    logic alarm_working;
    logic [31:0] alarm_address;
    logic [31:0] alarm_music_data[0:18] = {
        // 第一句：1 1 5 5 6 6 5 (一闪一闪亮晶晶)
        12,  // 1 - Do
        12,  // 1 - Do
        19,  // 5 - <PERSON>
        19,  // 5 - Sol
        21,  // 6 - La
        21,  // 6 - La
        19,  // 5 - <PERSON> (长音)
        19,  // 5 - Sol (延长)
        0,   // 休止
        // 第二句：4 4 3 3 2 2 1 (满天都是小星星)
        17,  // 4 - Fa
        17,  // 4 - Fa
        16,  // 3 - Mi
        16,  // 3 - Mi
        14,  // 2 - Re
        14,  // 2 - Re
        12,  // 1 - <PERSON> (长音)
        12,  // 1 - Do (延长)
        0,   // 短暂休止
        12   // 1 - <PERSON> (结束音)
    };

    logic oclock_working;
    logic [31:0] oclock_address;
    logic [31:0] oclock_music_data[0:7] = {
        // 简谱：1 3 5 1(高) - 1(高) 5 3 1
        12,  // 1 - <PERSON>
        16,  // 3 - Mi
        19,  // 5 - Sol
        24,  // 1(高) - Do(高音)
        0,   // - (休止符)
        24,  // 1(高) - Do(高音)
        19,  // 5 - Sol
        12   // 1 - Do (结束音)
    };

    logic warning_working;
    logic [31:0] warning_address;
    logic [31:0] warning_music_data[0:1] = {35, 0};

    logic countdown_working;
    logic [31:0] countdown_address;
    logic [31:0] countdown_music_data[0:7] = {
        // 简谱：5 6 7 1(高) - 1(高) 5 3 1
        19,  // 5 - Sol
        21,  // 6 - La
        23,  // 7 - Si
        24,  // 1(高) - Do(高音)
        0,   // - (休止符)
        24,  // 1(高) - Do(高音)
        19,  // 5 - Sol
        12   // 1 - Do
    };

    localparam ticking = `frequency / (4 * `audio_bps);
    logic [$clog2(ticking)-1:0] ms;
    always @(posedge clock or negedge reset_n) begin
        if (~reset_n) begin
            ms = 0;
            alarm_working = 0;
            alarm_address = 0;
            oclock_working = 0;
            oclock_address = 0;
            warning_working = 0;
            warning_address = 0;
            countdown_working = 0;
            countdown_address = 0;
        end else begin
            casex ({
                no_response, alarm, oclock, warning, countdown_finished
            })
                5'b1xxxx: begin
                    // 无响应状态：停止所有音频
                    ms = 0;
                    {alarm_working, oclock_working, warning_working, countdown_working} = 4'b0000;
                    {alarm_address, oclock_address, warning_address, countdown_address} = {32'h0, 32'h0, 32'h0, 32'h0};
                end
                5'b01xxx: begin
                    if (~alarm_working) begin
                        ms = 0;
                        alarm_working = 1;
                        alarm_address = 0;
                        oclock_working = 0;
                        oclock_address = 0;
                        warning_working = 0;
                        warning_address = 0;
                        countdown_working = 0;
                        countdown_address = 0;
                    end
                end
                5'b001xx: begin
                    if (~oclock_working) begin
                        ms = 0;
                        alarm_working = 0;
                        alarm_address = 0;
                        oclock_working = 1;
                        oclock_address = 0;
                        warning_working = 0;
                        warning_address = 0;
                        countdown_working = 0;
                        countdown_address = 0;
                    end
                end
                5'b0001x: begin
                    if (~warning_working) begin
                        ms = 0;
                        alarm_working = 0;
                        alarm_address = 0;
                        oclock_working = 0;
                        oclock_address = 0;
                        warning_working = 1;
                        warning_address = 0;
                        countdown_working = 0;
                        countdown_address = 0;
                    end
                end
                5'b00001: begin
                    if (~countdown_working) begin
                        ms = 0;
                        alarm_working = 0;
                        alarm_address = 0;
                        oclock_working = 0;
                        oclock_address = 0;
                        warning_working = 0;
                        warning_address = 0;
                        countdown_working = 1;
                        countdown_address = 0;
                    end
                end
            endcase

            if (|{alarm_working, oclock_working, warning_working, countdown_working}) begin
                if (ms == ticking - 1) begin
                    ms = 0;
                    case ({
                        alarm_working, oclock_working, warning_working, countdown_working
                    })
                        4'b1000: begin
                            if (alarm_address >= $size(alarm_music_data) - 1) begin
                                alarm_working = 0;
                                alarm_address = 0;
                            end else begin
                                alarm_address = alarm_address + 1;
                            end
                        end
                        4'b0100: begin
                            if (oclock_address >= $size(oclock_music_data) - 1) begin
                                oclock_working = 0;
                                oclock_address = 0;
                            end else begin
                                oclock_address = oclock_address + 1;
                            end
                        end
                        4'b0010: begin
                            if (warning_address >= $size(warning_music_data) - 1) begin
                                warning_working = 0;
                                warning_address = 0;
                            end else begin
                                warning_address = warning_address + 1;
                            end
                        end
                        4'b0001: begin
                            if (countdown_address >= $size(countdown_music_data) - 1) begin
                                countdown_working = 0;
                                countdown_address = 0;
                            end else begin
                                countdown_address = countdown_address + 1;
                            end
                        end
                    endcase
                end else begin
                    ms = ms + 1;
                end
            end
        end
    end

    always @(*) begin
        case ({
            alarm_working, oclock_working, warning_working, countdown_working
        })
            4'b1000: frequency_select <= alarm_music_data[alarm_address];
            4'b0100: frequency_select <= oclock_music_data[oclock_address];
            4'b0010: frequency_select <= warning_music_data[warning_address];
            4'b0001: frequency_select <= countdown_music_data[countdown_address];
            default: frequency_select <= ~0;
        endcase
    end

endmodule
