`ifndef _SYSTEM_CONFIG_H_
`define _SYSTEM_CONFIG_H_

// ========================================
// 系统状态定义
// ========================================
typedef enum logic [2:0] {
    normal_state,           // 正常显示模式
    time_setting_state,     // 时间设置模式
    alarm_setting_state,    // 闹钟设置模式
    timer_setting_state,    // 计时器模式
    countdown_setting_state // 倒计时模式
} state_t;

// ========================================
// 系统时钟配置
// ========================================
`define SYSTEM_FREQUENCY 100000000  // 系统时钟频率 100MHz
`define DISPLAY_FLIP_FREQ 4         // 显示闪烁频率
`define AUDIO_BPS 4                 // 音频节拍频率

// ========================================
// 系统常量定义
// ========================================
`define H_PTR_MAX 6          // 水平指针最大值（时间位数）
`define V_PTR_MAX 4          // 垂直指针最大值（闹钟数量）
`define ALARM_COUNT 4        // 闹钟数量
`define TIME_DIGITS 6        // 时间显示位数

// ========================================
// 按键映射定义
// ========================================
`define BTN_S1 0    // S1按键索引 - 水平左移/计时器启停
`define BTN_S2 1    // S2按键索引 - 水平右移/计时器清空
`define BTN_S3 2    // S3按键索引 - 垂直下移/倒计时启停
`define BTN_S4 3    // S4按键索引 - 模式切换
`define BTN_S5 4    // S5按键索引 - 垂直上移/倒计时清空

`endif
